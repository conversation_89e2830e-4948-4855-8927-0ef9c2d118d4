import 'dart:ui';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:lottie/lottie.dart' as llt;
import 'package:map_launcher/map_launcher.dart' as map_launcher;
import 'package:http/http.dart' as http;
import 'package:location/location.dart' as location;
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mapbox;
import 'package:mqtt_client/mqtt_client.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pinput/pinput.dart';
import 'package:polyline_animation_v1/polyline_animation_v1.dart';
import 'package:rooo_driver/features/care/screens/care_screen.dart';
import 'package:rooo_driver/features/edit_profile/screens/edit_profile_screen.dart';
import 'package:rooo_driver/features/online_offline_status/screens/online_offline_status_screen.dart';
import 'package:rooo_driver/features/permissions/screens/required_permissions_screen.dart';
import 'package:rooo_driver/features/registration/screens/complete_profile_screen.dart';
import 'package:rooo_driver/features/ride_flow/model/multiple_stop_model.dart';
import 'package:rooo_driver/features/ride_flow/model/online_offline_model.dart';
import 'package:rooo_driver/features/ride_flow/ride_repository/ride_repository.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen_utils.dart';
import 'package:rooo_driver/features/ride_flow/screen/selfie_image.dart';
import 'package:rooo_driver/features/ride_flow/service/heart_beat_service.dart';
import 'package:rooo_driver/features/vehicles/screens/add_vehicle_screen.dart';
import 'package:rooo_driver/features/vehicles/screens/vehicle_screen.dart';
import 'package:rooo_driver/global/constants/app_lottie.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/globalMethods/BackgroundAudio.dart';
import 'package:rooo_driver/global/globalMethods/local_waiting_time_model.dart';
import 'package:rooo_driver/global/globalMethods/one_signal_service.dart';
import 'package:rooo_driver/global/globalMethods/pausable_class.dart';
import 'package:rooo_driver/global/globalMethods/zego_service.dart';
import 'package:rooo_driver/global/widgets/loader_bottom_sheet.dart';
import 'package:rooo_driver/screens/dashboard/widgets/call_button.dart';
import 'package:rooo_driver/screens/dashboard/widgets/chat_count.dart';
import 'package:rooo_driver/screens/dashboard/widgets/offline_box.dart';
import 'package:rooo_driver/screens/dashboard/widgets/online_box.dart';
import 'package:rooo_driver/screens/dashboard/widgets/timer_widget.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../advertisements/models/advertisement_model.dart';

class RideScreen extends StatefulWidget {
  final RideModel? rideModel;
  final bool isNewSignUp;
  const RideScreen({
    super.key,
    this.rideModel,
    this.isNewSignUp = false,
  });

  @override
  State<RideScreen> createState() => _RideScreenState();
}

class _RideScreenState extends State<RideScreen> with WidgetsBindingObserver {
//mapbox

  bool _isOfflineAudioPlayed = false;
  bool _isOnlineAudioPlayed = false;

  bool _isDriverAnnotationCreated = false;
  bool _isAppInitDone = false;
  bool _areAllPermissionsAllowed = false;
  bool _isLocationPermissionForMapAllowed = false;
  bool _checkingMapLocationPermission = true;
  mapbox.MapboxMap? mapboxMap;
  late mapbox.PointAnnotationManager _pointAnnotationManager;
  List<mapbox.PointAnnotation> _mapAnnotations = [];

  late mapbox.PolylineAnnotationManager _polylineAnnotationManager;
  Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
  };

  _onMapCreated(mapbox.MapboxMap mapboxMap) async {
    this.mapboxMap = mapboxMap;
    // current location pluse
    await this
        .mapboxMap
        ?.location
        .updateSettings(mapbox.LocationComponentSettings(
          enabled: true,
          pulsingEnabled: true,
          puckBearingEnabled: true,
          // locationPuck: LocationPuck(
          //   locationPuck2D: DefaultLocationPuck2D(),
          // ),
        ));
        /*
        I want that camera must move as the current location moves
         */

    _pointAnnotationManager =
        await mapboxMap.annotations.createPointAnnotationManager();
    _polylineAnnotationManager =
        await mapboxMap.annotations.createPolylineAnnotationManager();
  }

  bool _isOfflineDialogBoxShown = false;
  double _cameraZoom = 18;

  num _driver_rating = 0.0;

  DateTime _lastTime = DateTime.now();

  List<map_launcher.Waypoint> _wayPoints = [];

  bool _isLocationPermisionGranted = false;

  location.Location locationPlugin = new location.Location();

  mapbox.PointAnnotation? _driverAnnotation;

  void _checkLocationPermission() {
    Permission.locationAlways.status.then((value) {
      _isLocationPermisionGranted = value == PermissionStatus.granted;
      if (_isLocationPermisionGranted) {
        _isLocationPermissionForMapAllowed = true;
        _onLocationPermissionGranted();
      }
      setState(() {
        _checkingMapLocationPermission = false;
      });
    });
  }

  void _checkNotificationPermission() {
    Permission.notification.status.then((value) {
      if (value == PermissionStatus.granted) {
        OneSignalService.init();
      }
    });
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    if (GlobalState.location_permission.value ==
            PermissionStatus.granted.name &&
        GlobalState.notification_permission.value ==
            PermissionStatus.granted.name) {
    } else {
      if (state == AppLifecycleState.resumed) {
        try {
          _checkLocationPermission();
          _checkNotificationPermission();

          GlobalState.is_foreground_state = true;
          if (_isOnline.value == 1 || _ride.value.id != null) {
            appLocation.enableBackgroundMode(enable: true);

            appLocationStream?.cancel();

            GlobalMethods.startsevertrackingTracking();
          } else if (_isOnline.value == 0) {
            appLocation.enableBackgroundMode(enable: false);
            appLocationStream?.cancel();
            GlobalMethods.stopServerTracking();
          }
          setState(() {
            _checkingMapLocationPermission = false;
          });
        } catch (e) {}
      } else if (state == AppLifecycleState.paused) {
        GlobalState.is_foreground_state = false;
        if (_isOnline.value == 1 || _ride.value.id != null) {
          GlobalState.driver_server_timer?.cancel();
          GlobalState.driver_server_timer = null;

          GlobalMethods.startsevertrackingTracking();
        }
      }
    }
  }

  LatLng _currentLocation = LatLng(0, 0);
  int _cancelledRideCount = 0;
  int _missedRideCount = 0;
  bool _isUserInteractingWithMap = false;
  Timer? _userInteractionTimer;

  AudioPlayer? _audioPlayer;

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  List<List<mapbox.Position>> _savedPoolPolylinePoint = [];
  Set<Marker> _savedPoolMarker = {};

  ValueNotifier<bool> isAudioPermissionGranted = ValueNotifier(false);
  CustomInfoWindowController _customInfoWindowController =
      CustomInfoWindowController();
  bool _homePageDataLoaded = false;
  HomePageDataModel? _homePageData;
  ValueNotifier<int> _isOnline = ValueNotifier(-1);
  PanelController _panelController = PanelController();
  PanelController _panelControllerForRideBottomSheet = PanelController();
  ScrollController _scrollController = ScrollController();
  num _currentEarningValue = 0;
  bool _isInProgressPolylineCreated = false;
  TextEditingController _startPasswordController = TextEditingController();
  ValueNotifier<RideModel> _ride = ValueNotifier(RideModel());
  ValueNotifier<OnRideRequest> _newride = ValueNotifier(OnRideRequest());

  ValueNotifier<OnRideRequest> _selectedOnrideRequest =
      ValueNotifier(OnRideRequest());
  ValueNotifier<List<OnRideRequest>> _poolRides = ValueNotifier([]);
  late StreamSubscription<RideFlowState> _rideFlowListenerListener;
  ValueNotifier<Set<int>> _arrived_otp_verified = ValueNotifier({});
  ValueNotifier<File> imagefile1 = ValueNotifier(File("path"));
  ValueNotifier<File> imagefile2 = ValueNotifier(File("path"));
  ValueNotifier<File> imagefile3 = ValueNotifier(File("path"));
  ValueNotifier<File> imagefile4 = ValueNotifier(File("path"));
  TutorialCoachMark? tutorialCoachMark;
  GlobalKey _earningKeyButton = GlobalKey();
  GlobalKey _onlineKeyButton = GlobalKey();
  GlobalKey _drawerKeyButton = GlobalKey();
  GlobalKey _blogKeyButton = GlobalKey();
  GlobalKey _currentLocationKeyButton = GlobalKey();
  GlobalKey _emergencyKeyButton = GlobalKey();
  GlobalKey keyButton5 = GlobalKey();
  GlobalKey keyBottomNavigation1 = GlobalKey();
  GlobalKey keyBottomNavigation2 = GlobalKey();
  GlobalKey keyBottomNavigation3 = GlobalKey();
  Timer? _stop_waiting_time_timer;
  ValueNotifier<int> _stop_waiting_time_timer_value = ValueNotifier(-1);
  bool? _isWaitingTimeStarted = false;
  ReceivePort _port = ReceivePort();
  Timer? _adminNotifyWaitingTimer;
  ValueNotifier<int> _adminNotifyWaitingTimeTimerValue = ValueNotifier(-1);
  Timer? _arrivedStateWaitingTimer;
  ValueNotifier<num> _arrivedStateWaitingTimerValue = ValueNotifier(-1);
  bool _isArrivedWaitingTimeNotifyAPICalled = false;
  bool _isStopWaitingTimeNotifyAPICalled = false;

  _startArrivedStateWaitingTimer({required int waiting_time}) {
    DateTime startTime = DateTime.now();
    if (waiting_time > 0) {
      startTime = startTime.subtract(Duration(seconds: waiting_time));
    }
    GlobalMethods.saveStartWaitingTimeData(
      data: LocalWaitingTimeModel(
        startTime: startTime,
        endTime: null,
        type: LocalWaitingTimeTyes.arrived,
      ),
    );
    if (!_isArrivedWaitingTimeNotifyAPICalled) {
      notifyWaitingTimeStarted(rideId: _ride.value.onRideRequest?.id ?? 0);
      _isArrivedWaitingTimeNotifyAPICalled = true;
    }
    _adminNotifyWaitingTimer?.cancel();
    _adminNotifyWaitingTimeTimerValue.value = -1;
    _arrivedStateWaitingTimerValue.value = waiting_time;
    _arrivedStateWaitingTimer?.cancel();
    _arrivedStateWaitingTimer = null;
    _arrivedStateWaitingTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      _arrivedStateWaitingTimerValue.value =
          _arrivedStateWaitingTimerValue.value + 1;
    });
  }

  Future<bool> _showTutorial() async {
    if (widget.isNewSignUp) {
      final BuildContext newContext = context;
      final NavigatorState navigator = Navigator.of(newContext);
      while (ModalRoute.of(context)?.isCurrent != true && navigator.canPop()) {
        navigator.pop();
        await Future.delayed(Duration(milliseconds: 100));
      }

      if (_scaffoldKey.currentState?.isDrawerOpen ?? false) {
        _scaffoldKey.currentState?.closeDrawer();
        await Future.delayed(Duration(milliseconds: 300));
      }

      _createTutorial();
      tutorialCoachMark?.show(context: context);
      return false;
    }
    _initApp();
    return true;
  }

  Future<void> _createDriverAnnotation(
      double latitude, double longitude) async {
    if (_isDriverAnnotationCreated == false) {
      final driverIcon = await rootBundle.load('images/ic_ car.png');
      var icon = driverIcon.buffer.asUint8List();
      _driverAnnotation = await _pointAnnotationManager.create(
        mapbox.PointAnnotationOptions(
          geometry: mapbox.Point(
            coordinates: mapbox.Position(
              longitude,
              latitude,
            ),
          ),
          image: icon,
          iconSize: 2,
        ),
      );
      _isDriverAnnotationCreated = true;
    }
  }

  Future<void> _onLocationPermissionGranted() async {
    await _createDriverAnnotation(
      0,
      0,
    );
    if (_isAppInitDone == false) {
      locationPlugin.onLocationChanged.listen((locationData) async {
        if (locationData.latitude == null) {
          return;
        }
        GlobalState.lastKnownLocation = locationData;

        try {
          if (_ride.value.onRideRequest != null &&
              _ride.value.onRideRequest!.status == ARRIVING) {
            await RideScreenUtils.onNearArrivedLocation(
              currentLatitude: locationData.latitude!.toDouble(),
              currentLongitude: locationData.longitude!.toDouble(),
              targetLatitude:
                  double.parse(_ride.value.onRideRequest!.startLatitude!),
              targetLongitude:
                  double.parse(_ride.value.onRideRequest!.startLongitude!),
              callback: () {
                _markArrived(
                    isAutomatic: true,
                    rideId: _ride.value.onRideRequest!.id!,
                    pickupLocation: LatLng(
                        double.parse(_ride.value.onRideRequest!.startLatitude!),
                        double.parse(
                            _ride.value.onRideRequest!.startLongitude!)),
                    currentLocation: LatLng(
                      locationData.latitude!.toDouble(),
                      locationData.longitude!.toDouble(),
                    ));
              },
            );
          }
          /* update point annotation */
          _driverAnnotation?.geometry = mapbox.Point(
            coordinates: mapbox.Position(
              locationData.longitude!.toDouble(),
              locationData.latitude!.toDouble(),
            ),
          );

          _pointAnnotationManager.update(_driverAnnotation!);

          if (_isUserInteractingWithMap) {
            return;
          }

          // await mapboxMap?.easeTo(
          //   mapbox.CameraOptions(
          //     bearing: locationData.heading!.toDouble(),
          //     zoom: _cameraZoom,
          //     center: mapbox.Point(
          //       coordinates: mapbox.Position(
          //         locationData.longitude!.toDouble(),
          //         locationData.latitude!.toDouble(),
          //       ),
          //     ),
          //   ),
          //   mapbox.MapAnimationOptions(
          //     duration: 1500,
          //   ),
          // );
        } catch (e) {
          print(e.toString());
        }
      });

      _startDeviceTracking().then((onValue) async {
        if (onValue) {
          _checkOnlineOfflineStatus();
          if (_homePageData?.is_live_photo_required ?? false) {
            if (GlobalState.isLiveImageScreenOpened == false) {
              _uploadSelfie();
            }
          }
          _mqttForUser();
          _getCurrentRide();
          // if (!widget.isNewSignUp) {
          //   Navigator.of(context).popUntil((route) => route.isFirst);
          // }
          await ZegoVoiceCallService.init(
            navigatorKey: navigatorKey,
            appId: AppCred.zegoAppId,
            appSign: AppCred.zegoAppSign,
            callerId: sharedPref.getInt(USER_ID).toString(),
            callerName: sharedPref.getString(FIRST_NAME).toString(),
          );
        }
      });

      setState(() {
        _isLocationPermisionGranted = true;
        _isLocationPermissionForMapAllowed = true;
      });
      _isAppInitDone = true;
    }
  }

  void _createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: Colors.red,
      textSkip: "Ok, got it",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onFinish: () {
        _initApp();
        GlobalMethods.toast("Tutorial completed, you are good to go");
      },
      onClickTarget: (target) {},
      onClickTargetWithTapPosition: (target, tapDetails) {},
      onClickOverlay: (target) {},
      onSkip: () {
        _initApp();
        return true;
      },
    );
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "Earnings",
        targetPosition: TargetPosition(
            Size(60, 60), Offset(MediaQuery.sizeOf(context).width / 2.3, 50)),
        // keyTarget: _earningKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Earnings",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "You can see your daily earnings here.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Earning button",
        keyTarget: _drawerKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Menu",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "You can see all other options here.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Earning button",
        keyTarget: _emergencyKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Emergency Contacts",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "You can access emergency contacts here,\n Also you can call 000.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Earning button",
        keyTarget: _currentLocationKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Current location",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "Click on this to get your location on the map.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Online button",
        keyTarget: _onlineKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        shape: ShapeLightFocus.RRect,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Go Online",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "Slide it to go online and get new ride request.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Blog panel",
        keyTarget: _blogKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        shape: ShapeLightFocus.RRect,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Blogs and other updates",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "Slide up or click here to see blogs and other updates.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    return targets;
  }

  Future<List<mapbox.Position>> getRouteCoordinates({
    required mapbox.Position origin,
    required mapbox.Position destination,
    List<mapbox.Position>? waypoints,
  }) async {
    try {
      String url = 'https://api.mapbox.com/directions/v5/mapbox/driving/'
          '${origin.lng},${origin.lat}';
      if (waypoints?.isNotEmpty ?? false) {
        String waypointStr = waypoints!
            .map((waypoint) => '${waypoint.lng},${waypoint.lat}')
            .join(';');
        url = '$url;$waypointStr';
      }
      url = '$url;${destination.lng},${destination.lat}'
          '?geometries=geojson&overview=full&steps=true&access_token=${AppCred.mapBoxPublicTokenKey}';
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final route = data['routes'][0];
        final geometry = route['geometry'];
        final coordinates = geometry['coordinates'];

        List<mapbox.Position> polylineCoordinates = [];
        for (var coord in coordinates) {
          polylineCoordinates.add(mapbox.Position(coord[0], coord[1]));
        }

        return polylineCoordinates;
      } else {
        throw Exception('Failed to load route');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<List<mapbox.Position>> createPolyline(
      {required mapbox.Position startLocation,
      required mapbox.Position endLocation,
      List<mapbox.Position>? waypoints,
      required int colors}) async {
    List<mapbox.Position> coordinates = await getRouteCoordinates(
        origin: startLocation, destination: endLocation, waypoints: waypoints);

    await _polylineAnnotationManager.create(mapbox.PolylineAnnotationOptions(
        lineOpacity: 1,
        lineWidth: 16,
        lineColor: AppColors.primaryMustardColr.value,
        // lineBorderWidth: 4,
        // lineBorderColor: colors,
        lineJoin: mapbox.LineJoin.ROUND,
        geometry: mapbox.LineString(coordinates: coordinates)));
    return coordinates;
  }

  Future<List<mapbox.Position>> _setPolyLines(
      {required double initialLat,
      required double initialLang,
      required double finalLat,
      required double finalLang,
      required Color colors,
      required int id,
      List<mapbox.Position>? savedPolylineCoordinates,
      List<mapbox.Position>? wayPoints}) async {
    await _polylineAnnotationManager.deleteAll();
    return await createPolyline(
        waypoints: wayPoints,
        colors: colors.value,
        startLocation: mapbox.Position(initialLang, initialLat),
        endLocation: mapbox.Position(finalLang, finalLat));
  }

  _ondestinationChangedInsideModel(
      {required StopsModel data, required int length}) async {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Column(
            children: [
              Text(
                "Rider changed the destination location",
                style: AppTextStyles.title(),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    data.title,
                    style: TextStyle(fontStyle: FontStyle.italic),
                  ),
                  height10,
                  Text(
                    "Updating the map....",
                  ),
                  AppLoader(
                    size: 12,
                  ),
                ],
              )
            ],
          ),
        );
      },
    );
    BackgroundAudio.playNewRideAudio();
    final Map<PolylineId, Polyline> _polylines = {};
    GoogleMapController? _controller;
    List<LatLng> list = [];
    ValueNotifier<bool> loaded = ValueNotifier(false);

    try {
      List<mapbox.Position> result = await getRouteCoordinates(
          origin: mapbox.Position(
              data.currentLat ?? GlobalState.driverPosition!.latitude,
              data.currentLng ?? GlobalState.driverPosition!.longitude),
          destination: mapbox.Position(data.stopLng, data.stopLng));

      if (result.isNotEmpty) {
        result.forEach((element) {
          list.add(LatLng(element.lat.toDouble(), element.lng.toDouble()));
        });

        Navigator.pop(context);
        showDialog(
          barrierDismissible: false,
          context: context,
          builder: (context) {
            final PolylineAnimator _animator = PolylineAnimator();

            void _startPolylineAnimation() {
              _animator.animatePolyline(
                list,
                'polyline_id',
                Colors.black,
                Colors.blue,
                _polylines,
                () {
                  loaded.notifyListeners();
                },
              );
            }

            return AlertDialog(
              contentPadding: EdgeInsets.zero,
              content: Column(
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height * .50,
                    child: ValueListenableBuilder(
                      valueListenable: loaded,
                      builder: (context, value, child) {
                        return GoogleMap(
                          onMapCreated: (controller) {
                            _controller = controller;
                            _startPolylineAnimation();
                          },
                          polylines: _polylines.values.toSet(),
                          initialCameraPosition: CameraPosition(
                            target: LatLng(
                                data.currentLat ??
                                    GlobalState.driverPosition!.latitude,
                                data.currentLng ??
                                    GlobalState.driverPosition!.longitude),
                            zoom: _cameraZoom,
                          ),
                        );
                      },
                    ),
                  ),
                  Padding(
                    padding: screenPadding,
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Destination changed"),
                            Text(length.toString() + " " + "times"),
                          ],
                        ),
                        Divider(),
                        Column(
                          children: [
                            Text(
                              "From",
                              style: AppTextStyles.title(),
                            ),
                            Text(
                              data.currentAdress ?? "",
                              maxLines: 10,
                            ),
                          ],
                        ),
                        height10,
                        Column(
                          children: [
                            Text(
                              "To",
                              style: AppTextStyles.title(),
                            ),
                            Text(
                              data.title,
                              maxLines: 10,
                            ),
                          ],
                        ),
                        AppButton(
                            text: "OK",
                            onPressed: () async {
                              BackgroundAudio.playSilence();
                              Navigator.pop(context);
                            })
                      ],
                    ),
                  )
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      BlocProvider.of<RideFlowCubit>(context)
          .emit(RideFlowErrorState(message: e.toString()));
      Navigator.pop(context);
    }
    ;
  }

  Future<void> cancel_ride_request_by_driver({
    required int rideId,
    required int reason_id,
    required List<Map<String, dynamic>> waitingCharges,
  }) async {
    Map request = {
      "id": rideId,
      "status": "canceled",
      "cancel_by": "driver",
      "reason_id": reason_id,
      "waiting_time_list": waitingCharges,
    };
    BlocProvider.of<RideFlowCubit>(context)
        .cancel_ride_request_by_driver(request: request, rideId: rideId);
  }

  _startStopTime({required int id}) {
    Map<String, dynamic> request = {
      "timer_start": DateTime.now().toString(),
      "id": id
    };

    BlocProvider.of<RideFlowCubit>(context).changeStopStatus(request: request);

    GlobalMethods.saveStartWaitingTimeData(
      data: LocalWaitingTimeModel(
        startTime: DateTime.now(),
        endTime: DateTime.now(),
        type: LocalWaitingTimeTyes.stop,
      ),
    );
  }

  _endStopTime(
      {required int id, DateTime? startTime, required bool iscanceled}) {
    DateTime _currentTime = DateTime.now();
    int? totalTime;
    if (startTime != null) {
      totalTime = _currentTime.difference(startTime).inSeconds;
    }
    Map<String, dynamic> request = {
      if (!iscanceled) "timer_end": _currentTime.toString(),
      "id": id,
      "status": "completed",
      if (!iscanceled) "total_time": totalTime
    };

    BlocProvider.of<RideFlowCubit>(context).changeStopStatus(request: request);
    GlobalMethods.saveEndWaitingTimeData(
      data: LocalWaitingTimeModel(
        startTime: DateTime.now(),
        endTime: DateTime.now(),
        type: LocalWaitingTimeTyes.stop,
      ),
    );
  }

  _reachedAtStop({required StopsModel stop}) {
    GlobalMethods.showConfirmationDialog(
        context: context,
        onPositiveAction: () async {
          Map<String, dynamic> request = {
            "is_arrived": true,
            "id": stop.id,
            "status": "arrived"
          };

          BlocProvider.of<RideFlowCubit>(context)
              .changeStopStatus(request: request);
          // }
        },
        title: "Are you sure you want to mark this stop as reached?");
  }

  _checkAdminNotifyWaitingTime({required OnRideRequest onrideRequest}) {
    int timeDifferenceInSeconds = 0;
    if (true) {
      bool isScheduled = onrideRequest.isSchedule == 1;
      GlobalState.adminNotificationTimers.setRideType(isScheduled: isScheduled);

      DateTime arrived_time = DateTime.parse(onrideRequest.arrived_time!);

      DateTime now = DateTime.now();
      timeDifferenceInSeconds = now.difference(arrived_time).inSeconds;
      if (timeDifferenceInSeconds > GlobalState.adminNotifyLimit) {
        _adminNotifyWaitingTimer?.cancel();
        _startArrivedStateWaitingTimer(
            waiting_time:
                timeDifferenceInSeconds - GlobalState.adminNotifyLimit);
      } else {
        _adminNotifyWaitingTimeTimerValue.value =
            GlobalState.adminNotifyLimit - timeDifferenceInSeconds;
        _adminNotifyWaitingTimer =
            Timer.periodic(Duration(seconds: 1), (timer) {
          if (_adminNotifyWaitingTimeTimerValue.value < 1) {
            _adminNotifyWaitingTimer?.cancel();

            timeDifferenceInSeconds = now.difference(arrived_time).inSeconds;
            if (_arrivedStateWaitingTimerValue.value == -1) {
              DateTime now = DateTime.now();

              timeDifferenceInSeconds = now.difference(arrived_time).inSeconds;

              if (timeDifferenceInSeconds > GlobalState.adminNotifyLimit) {
                _adminNotifyWaitingTimer?.cancel();
                _startArrivedStateWaitingTimer(
                    waiting_time:
                        timeDifferenceInSeconds - GlobalState.adminNotifyLimit);
              }
            }
          } else {
            _adminNotifyWaitingTimeTimerValue.value =
                _adminNotifyWaitingTimeTimerValue.value - 1;
          }
        });
      }
    }
  }

  _arrivedNotifyAdmin({required int rideId}) async {
    Map<String, dynamic> request = {"rideId": rideId};

    BlocProvider.of<RideFlowCubit>(context).notify_admin(request: request);
  }

  _getCurrentRide() async {
    BlocProvider.of<RideFlowCubit>(context).getCurrenRide().then((onValue) {
      setState(() {
        //
      });
    });
  }

  _checkOnlineOfflineStatus() {
    BlocProvider.of<RideFlowCubit>(context)
        .checkOnlineOfflineStatus(userId: sharedPref.getInt(USER_ID)!);
  }

  _getAppSetting() {
    BlocProvider.of<RideFlowCubit>(context).getAppSetting();
  }

  _mqttForUser() {
    BlocProvider.of<RideFlowCubit>(context).mqttForUser();
  }

  _changeOnlineOfflineStatus({required int makeOnline}) {
    BlocProvider.of<RideFlowCubit>(context).changeOnlineOfflineStatus2(
        makeOnline: makeOnline, userId: sharedPref.getInt(USER_ID)!);
  }

  _handleMissingRegion() {
    GlobalMethods.showInfoDialog(
        barrierDismissible: false,
        context: context,
        positiveAction: () async {
          bool? result = await GlobalMethods.pushScreen(
              context: context,
              screen: EditProfileScreen(
                isFromDahboard: true,
              ),
              screenIdentifier: ScreenIdentifier.EditProfileScreen);

          if (result == true) {
            Navigator.of(context).pop();
          }
        },
        title: language.handleMiddimgregionId);
  }

  _showAdvertisment({required AdvertisementModel advertisement}) {
    GlobalMethods.showAdvertismentDialog(
        context: context,
        positiveAction: () {
          launchUrl(Uri.parse(advertisement.webUrl));
        },
        title: "",
        imageUrl: advertisement.imageURL);
  }

  _getChatCount({required RideModel ride}) {
    if (ride.onRideRequest?.isPool ?? false) {
      if (ride.pool_rides == null) {
        if (!GlobalState.chat_count.value
            .any((chat) => chat.rideId == ride.onRideRequest!.id)) {
          GlobalState.chat_count.value.add(
              ChatCountModel(rideId: ride.onRideRequest!.id!, chatCount: 0));
        } else {
          GlobalState.chat_count.value
              .removeWhere((chat) => chat.rideId != ride.onRideRequest!.id!);
        }
      } else {
        ride.pool_rides?.forEach((rides) {
          if (!GlobalState.chat_count.value
              .any((chat) => chat.rideId == rides.id)) {
            GlobalState.chat_count.value
                .add(ChatCountModel(rideId: rides.id!, chatCount: 0));
          } else {
            GlobalState.chat_count.value
                .removeWhere((chat) => chat.rideId != rides.id);
          }
        });
      }
    } else {
      if (ride.onRideRequest != null) {
        if (!GlobalState.chat_count.value
            .any((chat) => chat.rideId != ride.onRideRequest!.id)) {
          GlobalState.chat_count.value.add(
              ChatCountModel(rideId: ride.onRideRequest!.id!, chatCount: 0));
        } else {
          GlobalState.chat_count.value
              .removeWhere((chat) => chat.rideId != ride.onRideRequest!.id!);
        }
      }
    }

    log(GlobalState.chat_count.value.toString());
  }

  Future<bool> _checkRequiredPermissions() async {
    var result = await Future.wait([
      Permission.notification.status,
      Permission.locationAlways.status,
      Permission.locationWhenInUse.status,
    ]);

    // _locationPermissionForMapAllowed = result[2] == PermissionStatus.granted;

    return result[0] == PermissionStatus.granted &&
        result[1] == PermissionStatus.granted;
  }

  Future<void> _startRideListner() async {
    _rideFlowListenerListener =
        context.read<RideFlowCubit>().stream.listen((state) async {
      if (state is RideFlowErrorState) {
        BackgroundAudio.playErrorAudio();

        GlobalMethods.errorToast(context, state.message);
      } else if (state is AppSettingLoadedState) {
        GlobalState.appSettingModel = state.appSettingModel;
        if (state.appSettingModel.settingModel?.notifyAdminWaitingTime !=
            null) {
          var timerSettings =
              state.appSettingModel.settingModel!.notifyAdminWaitingTime!;
          GlobalState.adminNotificationTimers.normalRideTimer =
              timerSettings.normalRideTime;
          GlobalState.adminNotificationTimers.scheduledRideTimer =
              timerSettings.scheduledRideTime;
        }
      } else if (state is HomePageDataLoadedState) {
        _onloadHomePageData(data: state.homePageData);
      } else if (state is AdvertismentDataLoadedState) {
        _showAdvertisment(advertisement: state.advertisement);
      } else if (state is OnlineErrorDataLoadedState) {
        getPositiveAction({required IncompleteSectionModel incomplete}) {
          if (incomplete.type == DRIVER_PENDINGS.quiz) {
            launchUrl(Uri.parse(incomplete.link));
          } else if (incomplete.type == DRIVER_PENDINGS.profileIncomplete) {
            GlobalMethods.pushScreen(
                context: context,
                screen: CompleteProfileScreen(
                  first_name: sharedPref.getString(FIRST_NAME)!,
                  last_name: sharedPref.getString(LAST_NAME)!,

                  // name: sharedPref.getString(Name),
                  userName: sharedPref.getString(USER_NAME),
                  id: sharedPref.getInt(USER_ID)!,
                  email: sharedPref.getString(USER_EMAIL),
                  gender: sharedPref.getString(GENDER),
                  otherGenderText: sharedPref.getString(OTHER_GENDER_TEXT),
                ),
                screenIdentifier: ScreenIdentifier.CompleteProfileScreen);
          } else if (incomplete.type == DRIVER_PENDINGS.subscriptionPending ||
              incomplete.type == DRIVER_PENDINGS.subscriptionPayment ||
              incomplete.type == DRIVER_PENDINGS.subscriptionExpired) {
            launchUrl(Uri.parse(incomplete.link));
          } else if (incomplete.type == DRIVER_PENDINGS.documentUpload ||
              incomplete.type == DRIVER_PENDINGS.documentApproval) {
            GlobalMethods.pushScreen(
                context: context,
                screen: DocumentScreen(canGoToDashboard: false),
                screenIdentifier: ScreenIdentifier.DocumentScreen);
          } else if (incomplete.type == DRIVER_PENDINGS.vehicleAdd) {
            GlobalMethods.pushScreen(
                context: context,
                screen: AddVehiclesScreen(),
                screenIdentifier: ScreenIdentifier.vehicleScreen);
          } else if (incomplete.type == DRIVER_PENDINGS.vehicleApproval ||
              incomplete.type == DRIVER_PENDINGS.vehicleInactive) {
            GlobalMethods.pushScreen(
                context: context,
                screen: VehiclesScreen(),
                screenIdentifier: ScreenIdentifier.vehicleScreen);
          } else if (incomplete.type == DRIVER_PENDINGS.profilePhotoMissing) {
            GlobalMethods.pushScreen(
                context: context,
                screen: EditProfileScreen(
                  isFromDahboard: false,
                ),
                screenIdentifier: ScreenIdentifier.EditProfileScreen);
          }
        }

        // Set driver status to offline after document submission
        _isOnline.value = 0;
        sharedPref.setInt(IS_ONLINE, 0);

        GlobalMethods.showOnlineOfflineDialog(
            lottieImage: AppLottie.sadEmoji,
            context: context,
            positiveAction: () {
              getPositiveAction(incomplete: state.data.incompleteList[0]);
            },
            title: Column(
                mainAxisSize: MainAxisSize.min,
                children: state.data.incompleteList.map((incomplete) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: Text(
                      incomplete.message,
                      style: AppTextStyles.title(),
                      textAlign: TextAlign.center,
                    ),
                  );
                }).toList()));
      } else if (state is OnlineState) {
        _isOfflineDialogBoxShown = false;

        // Only set online if explicitly requested through the online button
        if (state.isExplicitOnlineRequest) {
          _isOnline.value = 1;
          GlobalMethods.startsevertrackingTracking();
        } else {
          _isOnline.value = 0;
        }

        HeartBeatService.start(sharedPref.getInt(USER_ID).toString(),
            sharedPref.getString(TOKEN) ?? "");
        if (!_isOnlineAudioPlayed) {
          _isOnlineAudioPlayed = true;
          BackgroundAudio.playOnlineAudio();
        }
      } else if (state is OfflineState) {
        _onOffline();
      } else if (state is NewRideDeclineState) {
        _onDecline(rideId: state.rideId);
      } else if (state is NewRideAcceptState) {
        _onAccept();
      } else if (state is NewRideErrorState) {
        GlobalMethods.errorToast(context, state.message);
        void timerCallback() {
          GlobalState.new_ride_countdown_timer_value.value =
              GlobalState.pausableTimer.remainingTime;

          if (GlobalState.new_ride_countdown_timer_value.value < 2) {
            GlobalState.pausableTimer.cancel();
            _onTimeOut();
          }
        }

        GlobalState.pausableTimer.start(timerCallback);
      } else if (state is RideFlowErrorState) {
        GlobalMethods.errorToast(context, state.message);
      } else if (state is CurrentRideLoadedState) {
        await GlobalMethods.saveRideIdLocally(state.rideModel);

        if (state.rideModel.onRideRequest != null) {
          try {
            if (tutorialCoachMark?.isShowing ?? false) {
              tutorialCoachMark?.finish();
            }
          } catch (e) {}
          if (state.rideModel.onRideRequest!.status == COMPLETED) {
            if (state.rideModel.onRideRequest?.isDriverRated == 0) {
              GlobalMethods.replaceScreen(
                  screenIdentifier: ScreenIdentifier.rideScreen,
                  context: context,
                  screen: NewReviewScreen(
                    isPooling: false,
                    onrideRequest: state.rideModel.onRideRequest!,
                    rideId: state.rideModel.onRideRequest!.id!,
                  ));
            }
          }
          if (state.rideModel.onRideRequest!.stops != null) {
            _wayPoints = state.rideModel.onRideRequest!.stops!.map((element) {
              return map_launcher.Waypoint(
                element.stopLat,
                element.stopLng,
                element.title,
              );
            }).toList();
          }
        } else {
          GlobalMethods.removeWaitingTimeData();
        }

        GlobalState.current_ride = ValueNotifier(state.rideModel);
        _handleRideStateFunctions(ride: state.rideModel);
        _getChatCount(ride: state.rideModel);
      } else if (state is AdminNotifiedState) {
        _adminNotifyWaitingTimeTimerValue.value = -3;
      } else if (state is RideCancelledStateMqtt) {
        _onRideCancelled(rideId: state.rideId);
      } else if (state is MultipleStopMqtt) {
        BackgroundAudio.playNewRideAudio();
        GlobalMethods.showInfoDialogNew(
            context: context,
            onClick: () {
              Navigator.of(context).pop();
              BackgroundAudio.playSilence();
            },
            title: state.message);
        _getCurrentRide();
      } else if (state is MultipleStopDeletedMqtt) {
        BackgroundAudio.playErrorAudio();
        GlobalMethods.showInfoDialogNew(
            context: context,
            onClick: () {
              Navigator.of(context).pop();
            },
            title: state.message);
        _getCurrentRide();
      } else if (state is DriverCancelledStateMqtt) {
        _getCurrentRide();
      } else if (state is InitialRideLoadedMqtt) {
        _getCurrentRide();
      } else if (state is NewRideStateMqtt) {
        _onNewRide(onRideRequest: state.onRideRequest);
      } else if (state is DestinationChangedStateMqtt) {
        _ondestinationChangedInsideModel(
            data: state.onRideRequest.destinationPlace!.last,
            length: state.onRideRequest.destinationPlace!.length);
        _getCurrentRide();
      } else if (state is OfflineStateMqtt) {
        if (_isOnline.value == 1) {
          if (_isOfflineDialogBoxShown == false) {
            _isOfflineDialogBoxShown = true;

            GlobalMethods.showInfoDialog(
                onShowedDialog: () {
                  _isOfflineDialogBoxShown = true;
                },
                context: context,
                positiveAction: () {},
                title: state.messsage);

            _onOffline();
          }
        }
      } else if (state is MqttErrorState) {
        GlobalMethods.errorToast(
            context, "Server error: Please wait sometime and reload the app");
      } else if (state is DeviceLocationUpdatedState) {}
    });
  }

  init() async {
    _startRideListner();
    _onNoride();
  }

  Future<void> _initApp() async {
    _areAllPermissionsAllowed = await _checkRequiredPermissions();
    setState(() {
      _checkingMapLocationPermission = false;
    });
    _isOnline.value = -1;
    if (_areAllPermissionsAllowed == true) {
      _onLocationPermissionGranted();
      if (widget.rideModel != null) {
        GlobalMethods.startsevertrackingTracking();
        HeartBeatService.stop();
        _getChatCount(ride: widget.rideModel!);
        _handleRideStateFunctions(ride: widget.rideModel!);
      }
    }
  }

  Future<mapbox.PointAnnotation> _createMarkerPoint(
      {required mapbox.Position location,
      required String icon,
      required double iconSize}) async {
    final ByteData bytes = await rootBundle.load(icon);
    final Uint8List list = bytes.buffer.asUint8List();

    return await _pointAnnotationManager.create(
      mapbox.PointAnnotationOptions(
        image: list,
        iconSize: iconSize,
        geometry: mapbox.Point(
          coordinates: location,
        ),
      ),
    );
  }

  Future _setDestinationMarker({
    required MarkerId markerId,
    required LatLng endLocation,
    required String endIcon,
    required String endAddress,
  }) async {
    _mapAnnotations.add(
      await _createMarkerPoint(
          iconSize: .3,
          location:
              mapbox.Position(endLocation.longitude, endLocation.latitude),
          icon: endIcon),
    );
  }

  Future _setPickUpAndDestinationMarker({
    required mapbox.Position startLocation,
    required String startIcon,
    required String startAdress,
    required mapbox.Position endLocation,
    required String endIcon,
    required String endAddress,
    required String id,
  }) async {
    await _clearMapAnnotations();
    _mapAnnotations.add(
      await _createMarkerPoint(
          iconSize: .25, location: startLocation, icon: startIcon),
    );
    _mapAnnotations.add(
      await _createMarkerPoint(
          iconSize: .1, location: endLocation, icon: endIcon),
    );
  }

  _markReached(
      {required LatLng destinationLocation,
      required LatLng currentLocation,
      required OnRideRequest onrideRequest}) {
    GlobalMethods.showConfirmationDialog(
        context: context,
        onPositiveAction: () async {
          String end_location = await RideRepository().get_user_address();

          Map<String, dynamic> request = {
            "id": onrideRequest.id,
            "status": REACHED,
            "service_id": onrideRequest.serviceId!,
            "end_latitude": GlobalState.driverPosition!.latitude,
            "end_longitude": GlobalState.driverPosition!.longitude,
            "end_address": end_location,
            "distance": null,
            "waiting_time_list": GlobalMethods.getWaitingTimeData()
                .map((e) => e.toMap())
                .toList()
          };

          BlocProvider.of<RideFlowCubit>(context)
              .changeRideStatus(request: request, rideId: onrideRequest.id!);
        },
        title: "Are you sure you want to mark this ride as reached?");
  }

  _markInprogress({
    required LatLng pickupLocation,
    required LatLng currentLocation,
    required int rideId,
    required DateTime arrived_time,
  }) {
    GlobalMethods.showConfirmationDialog(
        context: context,
        onPositiveAction: () async {
          // bool result = await GlobalMethods.isCurrentLocationInDesiredArea(
          //     currentLocation: currentLocation, targetLocation: pickupLocation);
          bool result = true;
          if (result) {
            GlobalMethods.saveEndWaitingTimeData(
              data: LocalWaitingTimeModel(
                startTime: DateTime.now(),
                endTime: DateTime.now(),
                type: LocalWaitingTimeTyes.arrived,
              ),
            );

            _arrivedStateWaitingTimer?.cancel();
            _arrivedStateWaitingTimerValue = ValueNotifier(-1);
            DateTime now = DateTime.now();
            int arrived_waiting_time = now.difference(arrived_time).inSeconds -
                GlobalState.adminNotifyLimit;
            Map<String, dynamic> request = {
              "id": rideId,
              "status": IN_PROGRESS,
              "inprogress_time": now.toString(),
              if (arrived_waiting_time > 0)
                "arrived_waiting_time": arrived_waiting_time,
            };
            BlocProvider.of<RideFlowCubit>(context)
                .changeRideStatus(request: request, rideId: rideId);
          } else {
            GlobalMethods.errorToast(
                context, "You are not at the specified location");
          }
        },
        title:
            "Please ensure you are at the specified location. This operation will only proceed if you are there.");
  }

  _declineNewRide({
    required int reasonId,
    required int newRideId,
  }) {
    Map request = {
      "id": newRideId,
      "is_accept": "0",
      "is_flag": false,
      "driver_id": sharedPref.getInt(USER_ID),
      "reason": reasonId
    };
    BackgroundAudio.playSilence();
    GlobalState.pausableTimer.pause();
    BlocProvider.of<RideFlowCubit>(context).accepDeclineNewRideREquest(
        request: request, is_decline: true, rideId: newRideId);
  }

  _acceptNewRide({
    required int newRideId,
  }) {
    Map request = {
      "id": newRideId,
      "is_accept": "1",
      "driver_id": sharedPref.getInt(USER_ID),
    };
    BackgroundAudio.playSilence();
    GlobalState.pausableTimer.pause();

    BlocProvider.of<RideFlowCubit>(context).accepDeclineNewRideREquest(
      rideId: newRideId,
      request: request,
      is_decline: false,
    );
  }

  _endRide({required OnRideRequest onrideRequest}) {
    BlocProvider.of<RideFlowCubit>(context).complete_ride(
        ride_id: onrideRequest.id!,
        service_id: onrideRequest.serviceId!,
        total_distance: 10,
        waiting_time_list: []);
  }

  _markArrived({
    required int rideId,
    required LatLng pickupLocation,
    required LatLng currentLocation,
    bool isAutomatic = false,
  }) {
    if (isAutomatic) {
      Map<String, dynamic> request = {
        "id": rideId,
        "status": ARRIVED,
        "arrived_time": DateTime.now().toString()
      };
      BlocProvider.of<RideFlowCubit>(context)
          .changeRideStatus(request: request, rideId: rideId);
      return;
    }

    GlobalMethods.showConfirmationDialog(
        context: context,
        onPositiveAction: () async {
          bool result = await GlobalMethods.isCurrentLocationInDesiredArea(
              currentLocation: currentLocation, targetLocation: pickupLocation);
          if (result) {
            Map<String, dynamic> request = {
              "id": rideId,
              "status": ARRIVED,
              "arrived_time": DateTime.now().toString()
            };
            BlocProvider.of<RideFlowCubit>(context)
                .changeRideStatus(request: request, rideId: rideId);
          } else {
            GlobalMethods.errorToast(
                context, "You are not at the specified location");
          }
        },
        title:
            "Please ensure you are at the specified location. This operation will only proceed if you are there.");
  }

  _getHomePageData() {
    BlocProvider.of<RideFlowCubit>(context).getHomePageData();
  }

  Future<bool> _startDeviceTracking() async {
    bool result = true;

    await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    ).then((value) {
      GlobalState.driverPosition = LatLng(value.latitude, value.longitude);
    });
    await BlocProvider.of<RideFlowCubit>(context).get_device_current_location();
    await BlocProvider.of<RideFlowCubit>(context).startDeviceTracking();
    return result;
  }

  _onNoride() {
    GlobalState.homePageDataRefresher = () {
      _getHomePageData();
    };

    _getAppSetting();

    _getHomePageData();
  }

  _onPoolRide({required List<OnRideRequest> poolRide}) async {
    _poolRides.value = poolRide;

    _arrived_otp_verified.value =
        poolRide.map((element) => element.id!).toSet();

    Color getcolor({required int index}) {
      if (index == 0) {
        return Colors.black;
      } else if (index == 1) {
        return Colors.blue;
      }
      return Colors.orange;
    }

    await _polylineAnnotationManager.deleteAll();

    await _clearMapAnnotations();
    for (var i = 0; i < poolRide.length; i++) {
      List<mapbox.Position>? pool_coordinated = await _setPolyLines(
          id: poolRide[i].id!,
          initialLat: double.parse(poolRide[i].startLatitude!),
          initialLang: double.parse(poolRide[i].startLongitude!),
          finalLat: double.parse(poolRide[i].endLatitude!),
          finalLang: double.parse(poolRide[i].endLongitude!),
          savedPolylineCoordinates:
              _savedPoolPolylinePoint.length != poolRide.length
                  ? null
                  : _savedPoolPolylinePoint[i],
          colors: getcolor(index: i));

      await _setPickUpAndDestinationMarker(
          id: poolRide[i].id.toString(),
          startLocation: mapbox.Position(
              double.parse(poolRide[i].startLongitude!),
              double.parse(poolRide[i].startLatitude!)),
          startIcon: riderIcon,
          startAdress: poolRide[i].startAddress!,
          endLocation: mapbox.Position(double.parse(poolRide[i].endLongitude!),
              double.parse(poolRide[i].endLatitude!)),
          endIcon: DestinationIcon,
          endAddress: poolRide[i].endAddress!);

      if (pool_coordinated.isNotEmpty) {
        _savedPoolPolylinePoint.add(pool_coordinated);
      }
    }
  }

  _onNewRide({required OnRideRequest onRideRequest}) async {
    startNewRideCountdown({required int newRideId}) {
      GlobalState.pausableTimer.cancel();

      BackgroundAudio.playNewRideAudio();
      GlobalState.pausableTimer = PausableTimer(GlobalState.new_ride_countdown);
      GlobalState.new_ride_countdown_timer_value.value =
          GlobalState.new_ride_countdown;

      void timerCallback() {
        GlobalState.new_ride_countdown_timer_value.value =
            GlobalState.pausableTimer.remainingTime;

        if (GlobalState.new_ride_countdown_timer_value.value < 2) {
          GlobalState.pausableTimer.cancel();
          _onTimeOut();
        }
      }

      GlobalState.pausableTimer.start(timerCallback);
    }

    startNewRideCountdown(newRideId: onRideRequest.id!);

    _newride.value = onRideRequest;
    await _onMultipleStop(onRideRequest: onRideRequest);
  }

  _onArrivingState({required RideModel ride}) async {
    _ride.value = ride;
    await _polylineAnnotationManager.deleteAll();
    await _clearMapAnnotations();
    await _setPolyLines(
        id: ride.onRideRequest!.id!,
        colors: Colors.blue,
        initialLat: GlobalState.driverPosition!.latitude,
        initialLang: GlobalState.driverPosition!.longitude,
        finalLat: double.parse(ride.onRideRequest!.startLatitude.toString()),
        finalLang: double.parse(ride.onRideRequest!.startLongitude.toString()));

    await _setDestinationMarker(
        markerId: MarkerId("pickup"),
        endLocation: LatLng(
            double.parse(ride.onRideRequest!.startLatitude.toString()),
            double.parse(ride.onRideRequest!.startLongitude.toString())),
        endIcon: riderIcon,
        endAddress: ride.onRideRequest!.startAddress.toString());

    if (ride.onRideRequest!.stops != null) {
      await _addStopMarker(onrideRequest: ride.onRideRequest!);
    }
  }

  _onArrivedState({required RideModel ride}) async {
    GlobalMethods.succesToast(context, "At Pickup Location");
    _ride.value = ride;
    if (_adminNotifyWaitingTimer == null ||
        _adminNotifyWaitingTimer!.isActive == false) {
      _checkAdminNotifyWaitingTime(onrideRequest: ride.onRideRequest!);
    }
    await _polylineAnnotationManager.deleteAll();
    await _clearMapAnnotations();
    await _setPolyLines(
        id: ride.onRideRequest!.id!,
        colors: Colors.blue,
        initialLat: GlobalState.driverPosition!.latitude,
        initialLang: GlobalState.driverPosition!.longitude,
        finalLat: double.parse(ride.onRideRequest!.startLatitude.toString()),
        finalLang: double.parse(ride.onRideRequest!.startLongitude.toString()));

    await _setDestinationMarker(
        markerId: MarkerId("pickup"),
        endLocation: LatLng(
            double.parse(ride.onRideRequest!.startLatitude.toString()),
            double.parse(ride.onRideRequest!.startLongitude.toString())),
        endIcon: riderIcon,
        endAddress: ride.onRideRequest!.startAddress.toString());

    if (ride.onRideRequest!.stops != null) {
      await _addStopMarker(onrideRequest: ride.onRideRequest!);
    }
  }

  _onOffline() {
    GlobalMethods.stopServerTracking();
    _isOfflineDialogBoxShown = false;
    HeartBeatService.stop();
    _isOnline.value = 0;

    if (!_isOfflineAudioPlayed) {
      _isOfflineAudioPlayed = true;
      // BackgroundAudio.playOfflineAudio();
    }

    _panelController.animatePanelToPosition(0,
        duration: Duration(milliseconds: 200), curve: Curves.linear);
    _scrollController.animateTo(0,
        duration: Duration(milliseconds: 100), curve: Curves.linear);
  }

  _onDecline({required int rideId}) async {
    // BackgroundAudio.playOfflineAudio();

    if (_poolRides.value.isNotEmpty) {
      _onPoolRide(poolRide: _poolRides.value);
    } else {
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
    }

    _newride.value = OnRideRequest();
    _poolRides.notifyListeners();

    GlobalMethods.showInfoDialog(
        context: context,
        positiveAction: () {},
        title: language.RidedeclinedText);
  }

  _onloadHomePageData({required HomePageDataModel data}) async {
    _homePageData = data;

    await sharedPref.setInt(GLOBAL_REGION_ID, data.region_id ?? 0);

    await sharedPref.setInt(GLOBAL_REGION_ID, data.region_id ?? 0);
    global_region_id = data.region_id ?? 0;
    delete_account_instruction_text =
        data.account_delete_instructions_for_driver ?? "";
    about_us_instruction_text = data.about_us_instruction_driver ?? "";
    GlobalState.new_ride_countdown =
        data.ride_accept_decline_duration_for_driver_in_second ?? 30;
    _driver_rating = data.rating ?? 0.0;
    _currentEarningValue = data.today_earning ?? 0.0;

    getAndApplyCounters();
    setState(() {
      _homePageDataLoaded = true;
    });
    // if (_homePageData?.dashboardAd != null) {
    //   _showAdvertisment(advertisement: _homePageData!.dashboardAd!);
    // }
    // if (_is_profile_completed.value && data.region_id == null) {
    //   _handleMissingRegion();
    // }

    _showTutorial();
  }

  _uploadSelfie() {
    GlobalMethods.showInfoDialog(
        context: context,
        barrierDismissible: false,
        positiveAction: () async {
          GlobalMethods.pushScreen(
              context: context,
              screen: SelfieScreen(),
              screenIdentifier: ScreenIdentifier.SelfieScreen);
        },
        title: "For further rides, please upload one selfie ");
  }

  _onTimeOut() async {
    if (_poolRides.value.isNotEmpty) {
      _onPoolRide(poolRide: _poolRides.value);
    } else {
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
    }
    if (_missedRideCount == 0) {
      _missedRideCount = _missedRideCount + 1;
      GlobalMethods.showInfoDialog(
          negativeAction: () {
            _missedRideCount = 0;
          },
          context: context,
          positiveAction: () {
            _missedRideCount = 0;
          },
          title: language.youMissedARide);
    } else {
      if (_missedRideCount > 0) {
        _missedRideCount = _missedRideCount + 1;

        Navigator.pop(context);

        GlobalMethods.showInfoDialog(
            context: context,
            positiveAction: () {
              _missedRideCount = 0;
            },
            negativeAction: () {
              _missedRideCount = 0;
            },
            title: language.youMissedARide);
      }
    }
    _newride.value = OnRideRequest();
    _poolRides.notifyListeners();
  }

  _onDriverCancelled({required RideModel ride}) {
    GlobalMethods.infoToast(
        context, language.youHaveCanceledTheRidePleaseWaitForAdminApproval);
    _ride.value = ride;
  }

  Future _addStopMarker({required OnRideRequest onrideRequest}) async {
    String getStopOrder({required int index}) {
      if (index == 0) {
        return FirstStop;
      } else if (index == 1) {
        return SecondStop;
      } else if (index == 2) {
        return ThirdStop;
      }
      return "";
    }

    for (var i = 0; i < onrideRequest.stops!.length; i++) {
      await _setDestinationMarker(
          markerId: MarkerId(i.toString()),
          endLocation: LatLng(
              onrideRequest.stops![i].stopLat, onrideRequest.stops![i].stopLng),
          endIcon: getStopOrder(index: i),
          endAddress: onrideRequest.stops![i].title);
    }
  }

  Future _onMultipleStop({required OnRideRequest onRideRequest}) async {
    String getStopOrder({required int index}) {
      if (index == 0) {
        return FirstStop;
      } else if (index == 1) {
        return SecondStop;
      } else if (index == 2) {
        return ThirdStop;
      }
      return ThirdStop;
    }

    List<mapbox.Position>? polylineCoordinates = await _setPolyLines(
        id: onRideRequest.id!,
        colors: Colors.black,
        initialLat: double.parse(onRideRequest.startLatitude!),
        initialLang: double.parse(onRideRequest.startLongitude!),
        finalLat: double.parse(onRideRequest.endLatitude!),
        finalLang: double.parse(onRideRequest.endLongitude!),
        wayPoints: onRideRequest.stops == null
            ? null
            : onRideRequest.stops!.map((element) {
                return mapbox.Position(element.stopLng, element.stopLat);
              }).toList());

    if (polylineCoordinates.isNotEmpty ?? false) {
      await _setPickUpAndDestinationMarker(
          id: onRideRequest.id.toString(),
          startLocation: polylineCoordinates.first,
          startIcon: riderIcon,
          startAdress: onRideRequest.startAddress!,
          endLocation: polylineCoordinates.last,
          endIcon: DestinationIcon,
          endAddress: onRideRequest.endAddress!);
    }

    if (onRideRequest.stops != null) {
      for (var i = 0; i < onRideRequest.stops!.length; i++) {
        await _setDestinationMarker(
            markerId: MarkerId(i.toString()),
            endLocation: LatLng(onRideRequest.stops![i].stopLat,
                onRideRequest.stops![i].stopLng),
            endIcon: getStopOrder(index: i),
            endAddress: onRideRequest.stops![i].title);
      }
    }
  }

  _onRideCancelled({required int rideId}) async {
    GlobalState.pausableTimer.cancel();

    _adminNotifyWaitingTimer?.cancel();
    _arrivedStateWaitingTimer?.cancel();
    _arrivedStateWaitingTimerValue.value = -1;
    await _polylineAnnotationManager.deleteAll();
    await _clearMapAnnotations();

    _poolRides.notifyListeners();

    GlobalMethods.showInfoDialogNew(
      context: context,
      barrierDismissible: false,
      onClick: () {
        closeScreen(context);
        GlobalMethods.pushAndRemoveAll(
            context: context,
            screen: RideScreen(),
            screenIdentifier: ScreenIdentifier.InitialScreen);
      },
      title: "The ride has been canceled",
    );
  }

  _onAccept() async {
    if (_poolRides.value.isNotEmpty) {
      _onPoolRide(poolRide: _poolRides.value);
    } else {
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
    }
    GlobalMethods.succesToast(context, language.RideAcceptedTxt);
    BackgroundAudio.playSilence();
    _newride.value = OnRideRequest();

    _getCurrentRide();
  }

  _onInProgressState({required RideModel ride}) async {
    _ride.value = ride;

    _port.close();
    ReceivePort port = ReceivePort();
    _port = port;

    if (ride.onRideRequest!.isPool != null) {
      if (ride.onRideRequest!.isPool == false) {
        await sharedPref.setBool("IS_IN_PROGRESS", true);
      } else {
        await sharedPref.setBool("IS_IN_PROGRESS", false);
      }
    } else {
      if (ride.onRideRequest!.isPool == false) {
        sharedPref.setBool("IS_IN_PROGRESS", true);
      }
    }

    IsolateNameServer.removePortNameMapping(
      'back',
    );

    _isWaitingTimeStarted = sharedPref.getBool("isWaitingTimeStarted") ?? false;

    if (_isWaitingTimeStarted == true) {
      List<String>? waitingTimeList =
          sharedPref.getStringList("waitingTimeList") ?? [];
      Map<String, dynamic> lastWaitingTime = jsonDecode(waitingTimeList.last);
    }

    await _polylineAnnotationManager.deleteAll();
    await _clearMapAnnotations();

    if (ride.onRideRequest!.stops?.isNotEmpty ?? false) {
      await _onMultipleStop(onRideRequest: ride.onRideRequest!);
      if (!_isInProgressPolylineCreated) {
        _isInProgressPolylineCreated = true;
      }
    } else {
      await _setPickUpAndDestinationMarker(
          id: ride.onRideRequest!.id.toString(),
          startLocation: mapbox.Position(
              double.parse(ride.onRideRequest!.startLongitude!),
              double.parse(ride.onRideRequest!.startLatitude!)),
          startIcon: riderIcon,
          startAdress: ride.onRideRequest!.startAddress!,
          endLocation: mapbox.Position(
              double.parse(ride.onRideRequest!.endLongitude!),
              double.parse(ride.onRideRequest!.endLatitude!)),
          endIcon: DestinationIcon,
          endAddress: ride.onRideRequest!.endAddress!);
      await _setPolyLines(
          id: ride.onRideRequest!.id!,
          initialLat: double.parse(ride.onRideRequest!.startLatitude!),
          initialLang: double.parse(ride.onRideRequest!.startLongitude!),
          finalLat: double.parse(ride.onRideRequest!.endLatitude!),
          finalLang: double.parse(ride.onRideRequest!.endLongitude!),
          colors: Colors.black);
    }

    if (ride.onRideRequest!.stop_pending != null) {
      StopsModel _currentStop = ride.onRideRequest!.stop_pending!;

      if (_currentStop.status == "arrived") {
        if (_currentStop.timerStart != null) {
          Duration difference =
              DateTime.now().difference(_currentStop.timerStart!);
          _stop_waiting_time_timer_value.value = difference.inSeconds;
          _stop_waiting_time_timer?.cancel();
          _stop_waiting_time_timer = null;
          _stop_waiting_time_timer =
              Timer.periodic(Duration(seconds: 1), (timer) {
            _stop_waiting_time_timer_value.value =
                _stop_waiting_time_timer_value.value + 1;
          });

          _arrivedStateWaitingTimerValue.value = -1;
          _arrivedStateWaitingTimer?.cancel();

          _stop_waiting_time_timer_value.notifyListeners();
        } else {
          _stop_waiting_time_timer?.cancel();
          _stop_waiting_time_timer_value.value = 0;
        }
      } else {
        _stop_waiting_time_timer?.cancel();
        _stop_waiting_time_timer_value.value = 0;
      }
    }

    GlobalMethods.succesToast(context, language.inProgress);
  }

  _onReachedState({required RideModel ride}) async {
    sharedPref.reload();
    await sharedPref.remove("IS_IN_PROGRESS");
    _port.close();
    _arrivedStateWaitingTimer?.cancel();
    _ride.value = ride;
    await _polylineAnnotationManager.deleteAll();
    await _clearMapAnnotations();
  }

  _onPoolRideCompleted({required OnRideRequest onRideRequest}) async {
    bool reviewDone = await GlobalMethods.pushScreen(
        screenIdentifier: ScreenIdentifier.reviewScreen,
        context: context,
        screen: NewReviewScreen(
          onrideRequest: onRideRequest,
          rideId: onRideRequest.id!,
          isPooling: true,
        ));
    if (reviewDone) {
      _getCurrentRide();
    }
  }

  _onCompleted({required RideModel ride}) async {
    _ride.value = ride;
    sharedPref.reload();
    await sharedPref.remove("IS_IN_PROGRESS");
    await sharedPref.remove("serverLocations");
    await GlobalMethods.deletRideIdLocally(
        _ride.value.onRideRequest!.id.toString());
    await sharedPref.remove("isWaitingTimeStarted");
    await sharedPref.remove("lastLocation");
    await sharedPref.remove("newLocation");

    GlobalMethods.replaceScreen(
        screenIdentifier: ScreenIdentifier.reviewScreen,
        context: context,
        screen: NewReviewScreen(
          onrideRequest: ride.onRideRequest!,
          rideId: ride.onRideRequest!.id!,
          isPooling: false,
        ));
  }

  _driverCancelledWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      children: [
        SlidingUpPanel(
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text(
                      language.youCancelledARide,
                      style: AppTextStyles.header(),
                    ),
                    widgetMiddePart(onRideRequest: onRideRequest),
                    Container(
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          borderRadius: radius(), color: Colors.black),
                      child: CustomText(
                        data: language
                            .youHaveCanceledTheRidePleaseWaitForAdminApproval,
                        size: 15,
                        textAlign: TextAlign.center,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        )
      ],
    );
  }

  _noRideWidget() {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          BlocBuilder<RideFlowCubit, RideFlowState>(
            builder: (context, state) {
              return ValueListenableBuilder<int>(
                valueListenable: _isOnline,
                builder: (context, value, child) {
                  if (value == 0 || value == -1) {
                    return OnlineButton(
                        tutorial_key: _onlineKeyButton,
                        blocState: state,
                        onPress: () async {
                          GlobalMethods.handleInCompleteProfile(
                              context: context,
                              is_profile_completed:
                                  GlobalState.isProfileComplete,
                              positiveAction: () async {
                                var result = await Future.wait([
                                  Permission.notification.status,
                                  Permission.locationAlways.status,
                                ]);

                                bool isAllOk =
                                    result[0].isGranted && result[1].isGranted;
                                if (isAllOk == true) {
                                  _isOnlineAudioPlayed = false;
                                  _changeOnlineOfflineStatus(makeOnline: 1);
                                } else {
                                  Navigator.of(context).push<bool>(
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              RequiredPermissionScreen()));
                                }
                              });
                        });
                  }
                  return SizedBox();
                },
              );
            },
          ),
          BlocBuilder<RideFlowCubit, RideFlowState>(
            builder: (context, state) {
              return SlidingUpPanel(
                color: AppColors.whiteColor(context),
                controller: _panelController,
                minHeight: _isOnline.value == 1 ? 110 : 85,
                maxHeight: MediaQuery.of(context).size.height * .8,
                panelBuilder: (sc) {
                  _scrollController = sc;
                  return BlocBuilder<RideFlowCubit, RideFlowState>(
                    builder: (context, state) {
                      return SingleChildScrollView(
                        // controller: sc,
                        child: Column(
                          children: [
                            ValueListenableBuilder<int>(
                              valueListenable: _isOnline,
                              builder: (context, value, child) {
                                if (value == 1) {
                                  return OnlineBox(
                                    controller: _panelController,
                                  );
                                } else if (value == 0 || value == -1) {
                                  return OfflineBox(
                                    tutorial_key: _blogKeyButton,
                                    controller: _panelController,
                                  );
                                }
                                return SizedBox();
                              },
                            ),
                            InkWell(
                              onTap: () {
                                GlobalMethods.pushScreen(
                                    context: context,
                                    screen: OnlineOfflineStatusScreen(),
                                    screenIdentifier: ScreenIdentifier
                                        .OnlineOfflineStatusScreen);
                              },
                              child: Container(
                                  width: double.infinity,
                                  color: Colors.blue,
                                  padding: screenPadding,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        "Track online/offline time",
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 12,
                                        ),
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios_rounded,
                                        size: 12,
                                      )
                                    ],
                                  )),
                            ),
                            ScheduleRide(
                                scheduleRide:
                                    _homePageData?.scheduledRides ?? []),
                            Blogs(blogs: _homePageData?.blogs ?? []),
                            ValueListenableBuilder<int>(
                              valueListenable: _isOnline,
                              builder: (context, value, child) {
                                if (value == 1) {
                                  return OfflineButton(
                                      onPress: () {
                                        _isOfflineAudioPlayed = false;
                                        _changeOnlineOfflineStatus(
                                            makeOnline: 0);
                                      },
                                      blocState: state);
                                }
                                return SizedBox();
                              },
                            )
                          ],
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  _newRideWidget({required OnRideRequest newRide}) {
    ValueNotifier<bool> _showCancelRason = ValueNotifier(false);
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        SlidingUpPanel(
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          padding: screenPadding,
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Column(
                children: [
                  SlidingUpHandleContainer(),
                  Text(language.newRide, style: AppTextStyles.header()),
                  Divider(),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 4),
                          Text(language.pleaseAcceptTheRide,
                              style: AppTextStyles.title()),
                        ],
                      ),
                      Spacer(),
                      TimerWidget(
                          value: GlobalState.new_ride_countdown_timer_value),
                    ],
                  ),
                  Divider(),
                  height20,
                  AddressWidget(
                    onRideRequest: newRide,
                  ),
                  Divider(),
                  height20,
                  Row(
                    children: [
                      Expanded(
                          child: AppButton(
                              text: language.decline,
                              onPressed: () async {
                                GlobalMethods.showConfirmationDialog(
                                    context: context,
                                    onPositiveAction: () {
                                      _showCancelRason.value = true;
                                    },
                                    title: language
                                        .areYouSureYouWantToCancelThisRequest);
                              })),
                      SizedBox(width: 16),
                      Expanded(
                        child: AppButton(
                          onPressed: () async {
                            _acceptNewRide(newRideId: newRide.id!);
                          },
                          text: language.accept,
                        ),
                      ),
                    ],
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: _showCancelRason,
                    builder: (context, value, child) {
                      if (value) {
                        return Column(
                          children: [
                            height20,
                            Divider(),
                            Text(
                              "Reason",
                              style: AppTextStyles.header(),
                            ),
                            ListView.separated(
                                physics: NeverScrollableScrollPhysics(),
                                padding: screenPadding / 2,
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  return InkWell(
                                    onTap: () {
                                      _declineNewRide(
                                          reasonId:
                                              newRide.cancel_reasons![index].id,
                                          newRideId: newRide.id!);
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                          borderRadius: appRadius,
                                          color:
                                              AppColors.primaryColor(context)),
                                      padding: screenPadding,
                                      width: double.infinity,
                                      child: Text(
                                          newRide.cancel_reasons![index].name),
                                    ),
                                  );
                                },
                                separatorBuilder: (context, index) => height10,
                                itemCount: newRide.cancel_reasons?.length ?? 0),
                            height20,
                            height20,
                          ],
                        );
                      }
                      return SizedBox();
                    },
                  )
                ],
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        )
      ],
    );
  }

  _rideStateFunction({required RideModel ride}) {
    String status = ride.onRideRequest!.status!;
    _arrived_otp_verified.value.add(ride.onRideRequest!.id!);

    if (status == NEW_RIDE_REQUESTED) {
      _onNewRide(onRideRequest: ride.onRideRequest!);
    } else if (status == ARRIVING) {
      _onArrivingState(ride: ride);
    } else if (status == ARRIVED) {
      _onArrivedState(ride: ride);
    } else if (status == IN_PROGRESS) {
      _onInProgressState(ride: ride);
    } else if (status == REACHED) {
      _onReachedState(ride: ride);
    } else if (status == COMPLETED) {
      if (ride.onRideRequest!.isRiderRated != 1) {
        _onCompleted(ride: ride);
      }
    } else if (status == DRIVERCANCELED) {
      _onDriverCancelled(ride: ride);
    }
  }

  _handleRideStateFunctions({
    required RideModel ride,
  }) async {
    _ride.value = ride;
    _poolRides.value = ride.pool_rides ?? [];
    if (ZegoVoiceCallService.isinitialized.value == -1 ||
        ZegoVoiceCallService.isinitialized.value == 0) {
      if (!widget.isNewSignUp) {
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
      await ZegoVoiceCallService.init(
        navigatorKey: navigatorKey,
        appId: AppCred.zegoAppId,
        appSign: AppCred.zegoAppSign,
        callerId: sharedPref.getInt(USER_ID).toString(),
        callerName: sharedPref.getString(FIRST_NAME).toString(),
      );
    }

    if (ride.pool_rides != null) {
      if (ride.pool_rides!.length > 1) {
        _onPoolRide(poolRide: ride.pool_rides!);
      } else {
        _rideStateFunction(ride: ride);
      }
    } else {
      if (ride.onRideRequest != null) {
        _rideStateFunction(ride: ride);
      } else {
        GlobalMethods.removerStorageForWaitinfTime();
      }
    }
  }

  Widget RideBottomSheet({required OnRideRequest onRideRequest}) {
    String status = onRideRequest.status!;

    if (status == NEW_RIDE_REQUESTED) {
      return _newRideWidget(newRide: onRideRequest);
    } else if (status == ARRIVING) {
      return _arrivingWidget(onRideRequest: onRideRequest);
    } else if (status == ARRIVED) {
      return _arrivedWidget(onRideRequest: onRideRequest);
    } else if (status == IN_PROGRESS) {
      return _InprogressWidget(onRideRequest: onRideRequest);
    } else if (status == REACHED) {
      return _reachedWidget(onRideRequest: onRideRequest);
    } else if (status == DRIVERCANCELED) {
      return _driverCancelledWidget(onRideRequest: onRideRequest);
    }

    return SizedBox();
  }

  Widget _handleRideBottomSheet({required RideModel ride}) {
    if (ride.onRideRequest == null) {
      return _noRideWidget();
    } else {
      if (ride.pool_rides != null) {
        if (ride.pool_rides!.length > 1) {
          return poolRideWidget(poolrides: ride.pool_rides!);
        } else {
          return RideBottomSheet(onRideRequest: ride.pool_rides![0]);
        }
      } else {
        if (ride.onRideRequest != null) {
          return RideBottomSheet(onRideRequest: ride.onRideRequest!);
        }
      }

      return SizedBox();
    }
  }

  Widget _handlePoolRideBottomSheet({
    required OnRideRequest onRideRequest,
  }) {
    String status = onRideRequest.status!;
    if (status == NEW_RIDE_REQUESTED) {
      return _newRideWidget(newRide: onRideRequest);
    } else if (status == ARRIVING) {
      return _arrivingPoolwidget(onRideRequest: onRideRequest);
    } else if (status == ARRIVED) {
      return _arrivedPoolWidget(onRideRequest: onRideRequest);
    } else if (status == IN_PROGRESS) {
      return _inProgressPoolWidget(onRideRequest: onRideRequest);
    } else if (status == REACHED) {
      return _reachedPoolWidget(onRideRequest: onRideRequest);
    } else if (status == COMPLETED) {
      if (onRideRequest.isRiderRated == 0) {
        return _completdPoolWidget(onRideRequest: onRideRequest);
      }
    } else if (status == DRIVERCANCELED) {
      return _driverCancelledPoolWidget(onRideRequest: onRideRequest);
    }
    return SizedBox();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    BackgroundAudio.init();
    _checkNotificationPermission();
    GlobalState.isLoggedIn = true;
    GlobalState.isProfileComplete =
        sharedPref.getBool(IS_PROFILE_COMPLETE) ?? true;
    super.initState();
    afterBuildCreated(() {
      init();
      _checkLocationPermission();
    });
  }

  _onCameraChanged(cameraChangedEventData) {
    _isUserInteractingWithMap = true;
    _userInteractionTimer?.cancel();
    _userInteractionTimer = Timer(Duration(seconds: 5), () {
      _isUserInteractingWithMap = false;
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    GlobalState.isLoggedIn = false;
    _rideFlowListenerListener.cancel();
    _arrivedStateWaitingTimer?.cancel();
    _stop_waiting_time_timer?.cancel();
    _adminNotifyWaitingTimer?.cancel();
    GlobalState.driver_device_timer?.cancel();
    GlobalState.driver_server_timer?.cancel();
    _port.close();
    _audioPlayer?.dispose();
    super.dispose();
  }

  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          if (DateTime.now().difference(_lastTime).inSeconds <= 2) {
            SystemNavigator.pop();
          } else {
            _lastTime = DateTime.now();
            GlobalMethods.infoToast(context, "Double tap to exit");
          }
        }
      },
      child: Scaffold(
        key: _scaffoldKey,
        drawer: DrawerDriver(
          driver_rating: _driver_rating,
          scaffoldKey: _scaffoldKey,
          is_profile_completed: GlobalState.isProfileComplete,
        ),
        body: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Stack(
              children: [
                mapbox.MapWidget(
                  gestureRecognizers: gestureRecognizers,
                  onCameraChangeListener: _onCameraChanged,
                  key: ValueKey("mapWidget"),
                  onMapCreated: _onMapCreated,
                  cameraOptions: mapbox.CameraOptions(
                    zoom: _cameraZoom,
                    center: mapbox.Point(
                      coordinates: mapbox.Position(
                        GlobalState.driverPosition?.longitude ?? 10,
                        GlobalState.driverPosition?.latitude ?? 10,
                      ),
                    ),
                  ),
                ),
                _isLocationPermissionForMapAllowed == true ||
                        _checkingMapLocationPermission == true
                    ? const SizedBox()
                    : Center(
                        child: mapOverlayerIfNotGranted(context),
                      ),
              ],
            ),
            CustomInfoWindow(
              controller: _customInfoWindowController,
              height: 75,
              width: 150,
              offset: 50,
            ),
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: Platform.isIOS
                    ? MediaQuery.of(context).padding.top > 20
                        ? EdgeInsets.only(top: screenPaddingValue * 3)
                        : EdgeInsets.only(top: screenPaddingValue * 1)
                    : EdgeInsets.only(top: screenPaddingValue * 2),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        DrawerIcon(
                          tutorial_key: _drawerKeyButton,
                          scaffoldKey: _scaffoldKey,
                          is_profile_completed: GlobalState.isProfileComplete,
                          isDataLoaded: _homePageDataLoaded,
                        ),
                        CurrentEarningWidget(
                          keys: _earningKeyButton,
                          is_profile_completed: GlobalState.isProfileComplete,
                          current_earning: _currentEarningValue,
                          isDataLoaded: _homePageDataLoaded,
                        ),
                        SizedBox(
                          width: 60,
                        )
                      ],
                    ),
                    ValueListenableBuilder<int>(
                      valueListenable: ZegoVoiceCallService.isinitialized,
                      builder: (context, value, child) {
                        if (value == -1) {
                          return Container(
                            color: AppColors.primaryColor(context),
                            padding: screenPadding,
                            child: Row(
                              children: [
                                SizedBox(
                                  width: MediaQuery.of(context).size.width * .7,
                                  child: Text(
                                    "Call service error, please try to reload the app or tap on reload",
                                    maxLines: 4,
                                    style: TextStyle(
                                        // color: Colors.white,
                                        ),
                                  ),
                                ),
                                TextButton(
                                  child: Text(
                                    "Reload",
                                    style: TextStyle(color: Colors.red),
                                  ),
                                  onPressed: () {
                                    ZegoVoiceCallService.init(
                                      navigatorKey: navigatorKey,
                                      appId: AppCred.zegoAppId,
                                      appSign: AppCred.zegoAppSign,
                                      callerId:
                                          sharedPref.getInt(USER_ID).toString(),
                                      callerName: sharedPref
                                          .getString(FIRST_NAME)
                                          .toString(),
                                    );
                                  },
                                )
                              ],
                            ),
                          );
                        }

                        return SizedBox();
                      },
                    ),
                    ValueListenableBuilder(
                      valueListenable: GlobalState.MqttConnectionState,
                      builder: (context, value, child) {
                        if (GlobalState.MqttConnectionState.value ==
                                MqttConnectionState.connected.name ||
                            GlobalState.MqttConnectionState.value.isEmpty) {
                          return SizedBox();
                        }
                        return Container(
                          color: AppColors.primaryColor(context),
                          padding: screenPadding,
                          child: Row(
                            children: [
                              Row(
                                children: [
                                  SizedBox(
                                    width:
                                        MediaQuery.of(context).size.width * .7,
                                    child: Text(
                                      "Mqtt service error, please try to reload the app or report issue",
                                      maxLines: 4,
                                      style: TextStyle(
                                          // color: Colors.white,
                                          ),
                                    ),
                                  ),
                                  TextButton(
                                    child: Text(
                                      "Report",
                                      style: TextStyle(color: Colors.red),
                                    ),
                                    onPressed: () {
                                      GlobalMethods.pushScreen(
                                          context: context,
                                          screen: NewCareScreen(
                                              isPendingCare: true),
                                          screenIdentifier:
                                              ScreenIdentifier.NewCareScreen);
                                    },
                                  )
                                ],
                              )
                            ],
                          ),
                        );
                      },
                    ),
                    height10,
                  ],
                ),
              ),
            ),
            Positioned(
                left: 10,
                bottom: 140,
                child: Row(
                  children: [
                    SOSbutton(
                        tutorial_key: _emergencyKeyButton,
                        ride_id: 1,
                        region_id: 1),
                  ],
                )),
            _isLocationPermisionGranted
                ? Positioned(
                    right: .1,
                    bottom: 140,
                    child: CurrentLocationButton(
                        tutorial_key: _currentLocationKeyButton,
                        animate_map: () {
                          mapboxMap?.easeTo(
                              mapbox.CameraOptions(
                                  zoom: _cameraZoom,
                                  center: mapbox.Point(
                                      coordinates: mapbox.Position(
                                          GlobalState
                                                  .driverPosition?.longitude ??
                                              10,
                                          GlobalState
                                                  .driverPosition?.latitude ??
                                              10))),
                              mapbox.MapAnimationOptions());
                        }),
                  )
                : const SizedBox(),
            ValueListenableBuilder<OnRideRequest>(
              valueListenable: _newride,
              builder: (context, value, child) {
                if (value.id != null) {
                  return _newRideWidget(newRide: value);
                } else {
                  return ValueListenableBuilder(
                    valueListenable: _poolRides,
                    builder: (context, value, child) {
                      return ValueListenableBuilder<RideModel>(
                        valueListenable: _ride,
                        builder: (context, ride, child) {
                          if (_poolRides.value.length > 1) {
                            return poolRideWidget(poolrides: _poolRides.value);
                          } else {
                            if (ride.id != null) {
                              return _handleRideBottomSheet(ride: ride);
                            } else {
                              return _noRideWidget();
                            }
                          }
                        },
                      );
                    },
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  widgetMiddePart({required OnRideRequest onRideRequest}) {
    return Column(
      children: [
        height20,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              onRideRequest.riderName!,
              style: AppTextStyles.title(),
            ),
            RideSosButton(
                ride_id: onRideRequest.id!, region_id: onRideRequest.regionId!)
          ],
        ),
        onRideRequest.isPool != false
            ? Text(
                "Pool ride",
                style: AppTextStyles.title(),
              )
            : SizedBox(),
        Divider(),
        AddressWidget(
          onRideRequest: onRideRequest,
        ),
        Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
                child: ZegoCallButton(
              context: context,
              riderId: onRideRequest.riderId.toString(),
              riderName: onRideRequest.riderName!,
            )),
            Expanded(
                child: ChatButton(
              onRiderPlayerIdMissing: () {
                GlobalMethods.showConfirmationDialog(
                    context: context,
                    onPositiveAction: () {
                      _getCurrentRide();
                    },
                    title:
                        errorMessage + " " + "Please press ok and check agian");
              },
              onRideRequest: onRideRequest,
            )),
            onRideRequest.status == DRIVERCANCELED
                ? SizedBox()
                : onRideRequest.canDriverCancelTheRide == false
                    ? const SizedBox()
                    : Expanded(
                        child: RideCancelButton(onTap: () async {
                          bool? isDriverWantsToCancel = false;

                          /* check if waiting time is started and is above 3 minutes */
                          if (onRideRequest.status == "arrived" &&
                              _arrivedStateWaitingTimerValue.value < 181) {
                            isDriverWantsToCancel =
                                await GlobalMethods.showConfirmationDialog(
                              context: context,
                              positiveText: "Wait",
                              onPositiveAction: () {
                                // Navigator.of(context).pop(false);
                              },
                              negativeText: "Cancel",
                              onNegativeAction: () {
                                // Navigator.of(context).pop(true);
                              },
                              title:
                                  "Please wait for at least 3 minutes before canceling the ride. This allows for waiting charges to be applied, ensuring you receive some compensation for your time.",
                            );
                          }

                          if (!isDriverWantsToCancel) {
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return Dialog(
                                  child: Container(
                                    constraints: BoxConstraints(
                                      maxHeight:
                                          MediaQuery.of(context).size.height *
                                              0.7,
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          color: Colors.grey.shade200,
                                          height: 90,
                                          width: double.infinity,
                                          child: llt.Lottie.asset(
                                              "assets/lottie/alert.json"),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(16.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                "Please select reason to cancel this ride",
                                                style: TextStyle(
                                                  // color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              SizedBox(height: 16),
                                              Flexible(
                                                child: ListView.separated(
                                                  shrinkWrap: true,
                                                  physics:
                                                      ClampingScrollPhysics(),
                                                  itemCount: onRideRequest
                                                          .ride_cancel
                                                          ?.length ??
                                                      0,
                                                  separatorBuilder:
                                                      (context, index) =>
                                                          SizedBox(height: 8),
                                                  itemBuilder:
                                                      (context, index) {
                                                    return Material(
                                                      color: Colors.transparent,
                                                      child: InkWell(
                                                        onTap: () async {
                                                          Navigator.of(context)
                                                              .pop();
                                                          await GlobalMethods
                                                              .saveEndWaitingTimeData(
                                                            data:
                                                                LocalWaitingTimeModel(
                                                              startTime:
                                                                  DateTime
                                                                      .now(),
                                                              endTime: DateTime
                                                                  .now(),
                                                              type:
                                                                  LocalWaitingTimeTyes
                                                                      .stop,
                                                            ),
                                                          );
                                                          cancel_ride_request_by_driver(
                                                            rideId:
                                                                onRideRequest
                                                                    .id!,
                                                            reason_id:
                                                                onRideRequest
                                                                    .ride_cancel![
                                                                        index]
                                                                    .id,
                                                            waitingCharges:
                                                                GlobalMethods
                                                                        .getWaitingTimeData()
                                                                    .map((e) =>
                                                                        e.toMap())
                                                                    .toList(),
                                                          );
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                appRadius,
                                                            color: AppColors
                                                                .primaryColor(
                                                                    context),
                                                          ),
                                                          padding: EdgeInsets
                                                              .symmetric(
                                                                  vertical: 12,
                                                                  horizontal:
                                                                      16),
                                                          width:
                                                              double.infinity,
                                                          child: Text(
                                                            onRideRequest
                                                                .ride_cancel![
                                                                    index]
                                                                .name,
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 14,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: Text("Close"),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          }
                        }),
                      )
          ],
        ),
        Divider(),
      ],
    );
  }

  Widget _arrivingWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          language.arriving,
                          style: AppTextStyles.header(),
                        ),
                        SizedBox(height: 4),
                        Text(
                          "Ride #${onRideRequest.id}",
                          style: TextStyle(
                            color: AppColors.primaryColor(context),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    widgetMiddePart(onRideRequest: onRideRequest),
                    Column(
                      children: [
                        AppButton(
                            width: double.infinity,
                            text: "Mark Arrived",
                            onPressed: () async {
                              _markArrived(
                                  rideId: onRideRequest.id!,
                                  pickupLocation: LatLng(
                                      double.parse(
                                          onRideRequest.startLatitude!),
                                      double.parse(
                                          onRideRequest.startLongitude!)),
                                  currentLocation: GlobalState.driverPosition!);
                            }),
                        height10,
                        AppButton(
                          width: double.infinity,
                          text: language.startNavigationText,
                          onPressed: () async {
                            _panelControllerForRideBottomSheet
                                .animatePanelToPosition(
                              0.0,
                              duration: Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                            navigateToOuterMap(
                              context: context,
                              sourceCoords: map_launcher.Coords(
                                GlobalState.driverPosition!.latitude,
                                GlobalState.driverPosition!.longitude,
                              ),
                              source: "Current location",
                              destinationCoords: map_launcher.Coords(
                                double.parse(onRideRequest.startLatitude!),
                                double.parse(onRideRequest.startLongitude!),
                              ),
                              destination:
                                  onRideRequest.startAddress.toString(),
                              wayPoints: _wayPoints,
                            );
                          },
                        )
                      ],
                    )
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        )
      ],
    );
  }

  _poolPersonalDataWidget({required OnRideRequest onRideRequest}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 90,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 85,
                width: 85,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blackColor(context)),
                  image: DecorationImage(
                      image: NetworkImage(onRideRequest.riderProfileImage!)),
                  shape: BoxShape.circle,
                ),
              ),
              Text(
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                onRideRequest.riderName!,
                style: AppTextStyles.text(),
              )
            ],
          ),
        ),
        width20,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                maxLines: 4,
                onRideRequest.status!.toUpperCase(),
                style: AppTextStyles.title(color: AppColors.greenColor),
              ),
              height5,
              Text(
                "From:",
                style: TextStyle(
                  fontSize: 10,
                  fontStyle: FontStyle.italic,
                ),
              ),
              Text(
                  maxLines: 4,
                  onRideRequest.startAddress!,
                  style: AppTextStyles.text()),
              height10,
              Text(
                "To:",
                style: TextStyle(
                  fontSize: 10,
                  fontStyle: FontStyle.italic,
                ),
              ),
              Text(
                onRideRequest.endAddress!,
                maxLines: 4,
                style: AppTextStyles.text(),
              ),
            ],
          ),
        )
      ],
    );
  }

  _arrivingPoolwidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
                // width: double.infinity,
                text: language.arrived,
                onPressed: () async {
                  _markArrived(
                      rideId: onRideRequest.id!,
                      pickupLocation: LatLng(
                          double.parse(onRideRequest.startLatitude!),
                          double.parse(onRideRequest.startLongitude!)),
                      currentLocation: GlobalState.driverPosition!);
                }),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                        context: context,
                        onPositiveAction: () {
                          _getCurrentRide();
                        },
                        title:
                            errorMessage + "Please press ok and check agian");
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
          // ),
        ),
      ),
    );
  }

  Widget _arrivedPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            ValueListenableBuilder<Set<int>>(
              valueListenable: _arrived_otp_verified,
              builder: (context, value, child) {
                if (onRideRequest.isOtpEnable ?? false) {
                  if (value.contains(onRideRequest.id)) {
                    return AppButton(
                        text: "Verify",
                        onPressed: () async {
                          _otpVerificationDialog(
                              onRideRequest: onRideRequest,
                              context: context,
                              otp: onRideRequest.otp.toString(),
                              controller: _startPasswordController,
                              onSuccess: () {
                                _arrived_otp_verified.value
                                    .remove(onRideRequest.id);
                                _arrived_otp_verified.notifyListeners();
                                _startPasswordController.clear();
                              });
                        });
                  } else {
                    return AppButton(
                        text: "Start Ride",
                        onPressed: () async {
                          _markInprogress(
                              arrived_time:
                                  DateTime.parse(onRideRequest.arrived_time!),
                              pickupLocation: LatLng(
                                  double.parse(onRideRequest.startLatitude!),
                                  double.parse(onRideRequest.startLongitude!)),
                              currentLocation: GlobalState.driverPosition!,
                              rideId: onRideRequest.id!);
                        });
                  }
                } else {
                  return AppButton(
                      text: language.StartRideText,
                      onPressed: () async {
                        _markInprogress(
                            arrived_time:
                                DateTime.parse(onRideRequest.arrived_time!),
                            pickupLocation: LatLng(
                                double.parse(onRideRequest.startLatitude!),
                                double.parse(onRideRequest.startLongitude!)),
                            currentLocation: GlobalState.driverPosition!,
                            rideId: onRideRequest.id!);
                      });
                }
              },
            ),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                        context: context,
                        onPositiveAction: () {
                          _getCurrentRide();
                        },
                        title:
                            errorMessage + "Please press ok and check agian");
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _driverCancelledPoolWidget({required OnRideRequest onRideRequest}) {
    return _slidingContainer(
        onRideRequest: onRideRequest,
        child: Column(
          children: [
            Row(
              children: [
                _poolPersonalDataWidget(onRideRequest: onRideRequest),
                Spacer(),
                Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      borderRadius: radius(), color: Colors.black),
                  child: CustomText(
                    data: "Cancelled",
                    size: 15,
                    textAlign: TextAlign.center,
                    color: Colors.white,
                  ),
                ),
              ],
            )
          ],
        ));
  }

  _completdPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
                text: "Rate",
                onPressed: () async {
                  _onPoolRideCompleted(onRideRequest: onRideRequest);
                }),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                        context: context,
                        onPositiveAction: () {
                          _getCurrentRide();
                        },
                        title:
                            errorMessage + "Please press ok and check agian");
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _reachedPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
                text: language.endRide,
                onPressed: () async {
                  _endRide(onrideRequest: onRideRequest);
                }),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                        context: context,
                        onPositiveAction: () {
                          _getCurrentRide();
                        },
                        title:
                            errorMessage + "Please press ok and check agian");
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _inProgressPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
              text: language.reached,
              onPressed: () async {
                _markReached(
                    destinationLocation: LatLng(
                        double.parse(onRideRequest.endLatitude!),
                        double.parse(onRideRequest.endLongitude!)),
                    currentLocation: GlobalState.driverPosition!,
                    onrideRequest: onRideRequest);
              },
            ),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                        context: context,
                        onPositiveAction: () {
                          _getCurrentRide();
                        },
                        title:
                            errorMessage + "Please press ok and check agian");
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _arrivedWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text(
                      language.arrived,
                      style: AppTextStyles.header(),
                    ),
                    SizedBox(height: 4),
                    Text(
                      "Ride #${onRideRequest.id}",
                      style: TextStyle(
                        color: AppColors.primaryColor(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 16),
                    ValueListenableBuilder<num>(
                      valueListenable: _arrivedStateWaitingTimerValue,
                      builder: (context, state, _) {
                        if (state == -1 || state == -2) {
                          return SizedBox();
                        } else if (state > 0) {
                          return Container(
                            decoration: BoxDecoration(
                                borderRadius: appRadius,
                                color: Colors.white,
                                border: Border.all()),
                            padding: EdgeInsets.symmetric(
                                horizontal: 15, vertical: 5),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomText(
                                      color: Colors.black,
                                      data: intToTimeLeftInSeconds(
                                          _arrivedStateWaitingTimerValue.value
                                              .toInt()),
                                      size: 20,
                                      fontweight: FontWeight.bold,
                                    ),
                                    height5,
                                    CustomText(
                                      data: language.waitTime,
                                      size: 15,
                                      color: Colors.blue,
                                    ),
                                    width5,
                                  ],
                                ),
                              ],
                            ),
                          );
                        }
                        return SizedBox();
                      },
                    ),
                    ValueListenableBuilder<int>(
                        valueListenable: _adminNotifyWaitingTimeTimerValue,
                        builder: (context, state, _) {
                          if (state == -2) {
                            return custom_loader();
                          }
                          if (state == -3) {
                            return SizedBox(
                              width: double.infinity,
                              child: Card(
                                color: Colors.green,
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Center(
                                    child: Text(language.adminNotifiedTxt,
                                        style: TextStyle(color: Colors.white)),
                                  ),
                                ),
                              ),
                            );
                          }

                          if (state == -3) {
                            return AppButton(
                                width: double.infinity,
                                text: language.notifyAdmin,
                                onPressed: () async {
                                  GlobalMethods.showConfirmationDialog(
                                      context: context,
                                      onPositiveAction: () {
                                        _arrivedNotifyAdmin(
                                            rideId: onRideRequest.id!);
                                      },
                                      title: language
                                          .AreYouSureYouWantToNotifyAdminTxt);
                                });
                          }

                          if (state > 0) {
                            return Card(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(language.NotifyToAdminAboutLateRideTxt,
                                        style: TextStyle(
                                          fontSize: 12,
                                        )),
                                    Text(
                                      intToTimeLeftInSeconds(
                                          _adminNotifyWaitingTimeTimerValue
                                              .value),
                                      style: AppTextStyles.header(),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }

                          return SizedBox();
                        }),
                    widgetMiddePart(onRideRequest: onRideRequest),
                    ValueListenableBuilder<Set<int>>(
                      valueListenable: _arrived_otp_verified,
                      builder: (context, value, child) {
                        if (onRideRequest.isOtpEnable ?? false) {
                          if (_arrived_otp_verified.value
                              .contains(onRideRequest.id)) {
                            return AppButton(
                                width: double.infinity,
                                text: "Verify",
                                onPressed: () async {
                                  _otpVerificationDialog(
                                      onRideRequest: onRideRequest,
                                      context: context,
                                      otp: onRideRequest.otp.toString(),
                                      controller: _startPasswordController,
                                      onSuccess: () {
                                        _arrived_otp_verified.value
                                            .remove(onRideRequest.id);
                                        _arrived_otp_verified.notifyListeners();
                                        _startPasswordController.clear();
                                      });
                                });
                          } else {
                            return AppButton(
                                width: double.infinity,
                                text: language.StartRideText,
                                onPressed: () async {
                                  _markInprogress(
                                      arrived_time: DateTime.parse(
                                          onRideRequest.arrived_time!),
                                      pickupLocation: LatLng(
                                          double.parse(
                                              onRideRequest.startLatitude!),
                                          double.parse(
                                              onRideRequest.startLongitude!)),
                                      currentLocation:
                                          GlobalState.driverPosition!,
                                      rideId: onRideRequest.id!);
                                });
                          }
                        } else {
                          return AppButton(
                              width: double.infinity,
                              text: language.StartRideText,
                              onPressed: () async {
                                _markInprogress(
                                    arrived_time: DateTime.parse(
                                        onRideRequest.arrived_time!),
                                    pickupLocation: LatLng(
                                        double.parse(
                                            onRideRequest.startLatitude!),
                                        double.parse(
                                            onRideRequest.startLongitude!)),
                                    currentLocation:
                                        GlobalState.driverPosition!,
                                    rideId: onRideRequest.id!);
                              });
                        }
                      },
                    )
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        )
      ],
    );
  }

  Widget _InprogressWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text(
                      language.inProgress,
                      style: AppTextStyles.header(),
                    ),
                    SizedBox(height: 4),
                    Text(
                      "Ride #${onRideRequest.id}",
                      style: TextStyle(
                        color: AppColors.primaryColor(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    widgetMiddePart(onRideRequest: onRideRequest),
                    _destinationChangedView(onRideRequest),
                    onRideRequest.stop_pending != null
                        ? ValueListenableBuilder(
                            valueListenable: _stop_waiting_time_timer_value,
                            builder: (context, value, child) {
                              StopsModel _currentStop =
                                  onRideRequest.stop_pending!;

                              if (_currentStop.status == null) {
                                return Padding(
                                  padding: const EdgeInsets.only(top: 10.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Next Stop"),
                                      Material(
                                        color: Colors.grey.shade300,
                                        child: InkWell(
                                          splashColor:
                                              AppColors.primaryColor(context),
                                          onTap: () {
                                            StopsModel selectedStop =
                                                onRideRequest.stops!.firstWhere(
                                                    (element) =>
                                                        element.status == null);

                                            _reachedAtStop(stop: selectedStop);
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(8)),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.back_hand,
                                                  color: Colors.red,
                                                ),
                                                width15,
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        "Tap to mark reached at",
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                      Text(
                                                        _currentStop.title,
                                                        maxLines: 3,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: TextStyle(
                                                          // fontSize: 12,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                InkWell(
                                                    onTap: () {
                                                      _showAllStops(
                                                          onRideRequest.stops!);
                                                    },
                                                    child: Icon(
                                                      Icons.list,
                                                      color: Colors.black,
                                                    )),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      height20,
                                      AppButton(
                                          width: double.maxFinite,
                                          text: language.startNavigationText,
                                          onPressed: () async {
                                            _panelControllerForRideBottomSheet
                                                .animatePanelToPosition(
                                              0.0,
                                              duration:
                                                  Duration(milliseconds: 300),
                                              curve: Curves.easeInOut,
                                            );
                                            navigateToOuterMap(
                                              context: context,
                                              sourceCoords: map_launcher.Coords(
                                                GlobalState
                                                    .driverPosition!.latitude,
                                                GlobalState
                                                    .driverPosition!.longitude,
                                              ),
                                              source: "Current location",
                                              destinationCoords:
                                                  map_launcher.Coords(
                                                double.parse(
                                                    onRideRequest.endLatitude!),
                                                double.parse(onRideRequest
                                                    .endLongitude!),
                                              ),
                                              destination:
                                                  onRideRequest.endAddress ??
                                                      "",
                                              wayPoints: _wayPoints,
                                            );
                                          })
                                    ],
                                  ),
                                );
                              } else if (_currentStop.status == "arrived") {
                                return _getArrivedAtStopWidget(_currentStop);
                              }

                              return SizedBox();
                            },
                          )
                        : Column(
                            children: [
                              AppButton(
                                width: double.infinity,
                                text: "Mark Reached",
                                onPressed: () async {
                                  _markReached(
                                      destinationLocation: LatLng(
                                          double.parse(
                                              onRideRequest.endLatitude!),
                                          double.parse(
                                              onRideRequest.endLongitude!)),
                                      currentLocation:
                                          GlobalState.driverPosition!,
                                      onrideRequest: onRideRequest);
                                },
                              ),
                              height20,
                              AppButton(
                                  width: double.maxFinite,
                                  text: language.startNavigationText,
                                  onPressed: () async {
                                    _panelControllerForRideBottomSheet
                                        .animatePanelToPosition(
                                      0.0,
                                      duration: Duration(milliseconds: 300),
                                      curve: Curves.easeInOut,
                                    );
                                    navigateToOuterMap(
                                      context: context,
                                      sourceCoords: map_launcher.Coords(
                                        GlobalState.driverPosition!.latitude,
                                        GlobalState.driverPosition!.longitude,
                                      ),
                                      source: "Current location",
                                      destinationCoords: map_launcher.Coords(
                                        double.parse(
                                            onRideRequest.endLatitude!),
                                        double.parse(
                                            onRideRequest.endLongitude!),
                                      ),
                                      destination:
                                          onRideRequest.endAddress.toString(),
                                      wayPoints: _wayPoints,
                                    );
                                  })
                            ],
                          ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        )
      ],
    );
  }

  Widget _getArrivedAtStopWidget(StopsModel stop) {
    late Widget child;

    if (stop.timerStart == null) {
      child = Column(
        children: [
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("Wait timer?"),
              Row(
                children: [
                  AppButton(
                      backgroundColor: Colors.grey.shade300,
                      text: "Start",
                      onPressed: () async {
                        _startStopTime(id: stop.id);
                        if (!_isStopWaitingTimeNotifyAPICalled) {
                          notifyWaitingTimeStarted(
                              rideId: _ride.value.onRideRequest?.id ?? 0);
                          _isStopWaitingTimeNotifyAPICalled = true;
                        }
                      }),
                  width5,
                  AppButton(
                      text: "Continue ride",
                      onPressed: () async {
                        _endStopTime(
                          iscanceled: true,
                          id: stop.id,
                        );
                      })
                ],
              )
            ],
          ),
          const Divider(),
        ],
      );
    } else {
      child = Column(
        children: [
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(intToTimeLeftInSeconds(
                  _stop_waiting_time_timer_value.value.toInt())),
              AppButton(
                  text: "Continue ride",
                  onPressed: () async {
                    _endStopTime(
                        iscanceled: false,
                        id: stop.id,
                        startTime: stop.timerStart!);
                  })
            ],
          ),
          const Divider(),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "You are At:",
          style: TextStyle(
            fontSize: 10,
          ),
        ),
        Text(stop.title),
        Container(
          padding: const EdgeInsets.all(8),
          // color: Colors.grey.shade200,
          child: Row(
            children: [
              Icon(Icons.info),
              width5,
              Expanded(
                  child: stop.timerStart == null
                      ? Text(
                          "If it is taking longer, You can start the wait timer.",
                          style: TextStyle(fontStyle: FontStyle.italic))
                      : Text("You started the waiting timer...",
                          style: TextStyle(fontStyle: FontStyle.italic)))
            ],
          ),
        ),
        child
      ],
    );
  }

  Widget _reachedWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text(
                      language.reached,
                      style: AppTextStyles.header(),
                    ),
                    widgetMiddePart(onRideRequest: onRideRequest),
                    AppButton(
                        width: double.infinity,
                        text: language.endRide,
                        onPressed: () async {
                          _endRide(onrideRequest: onRideRequest);
                        }),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        )
      ],
    );
  }

  void _otpVerificationDialog(
      {required OnRideRequest onRideRequest,
      required BuildContext context,
      required String otp,
      required TextEditingController controller,
      required void Function() onSuccess}) {
    showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: inkWellWidget(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(4),
                    decoration: BoxDecoration(shape: BoxShape.circle),
                    child: Icon(Icons.close, size: 20, color: Colors.white),
                  ),
                ),
              ),
              SizedBox(height: 8),
              Center(
                child: Text(language.passCode,
                    style: AppTextStyles.header(), textAlign: TextAlign.center),
              ),
              SizedBox(height: 16),
              Text(
                language.enterthePasscodewhichIsDisplayingInTheCustomersMobile,
                style: AppTextStyles.title(),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              Pinput(
                controller: controller,
                length: 4,
                onCompleted: (pin) {
                  // _otp = pin;
                },
              ),
              height10,
              AppButton(
                width: double.infinity,
                text: language.verify,
                onPressed: () async {
                  if (controller.text.length < 4 || controller.text != otp) {
                    GlobalMethods.infoToast(
                        context, language.pleaseEnterValidOtp);
                    controller.clear();
                  } else {
                    Navigator.pop(context);
                    onSuccess();
                  }
                },
              )
            ],
          ),
        );
      },
    );
  }

  Widget _destinationChangedView(OnRideRequest ride) {
    if (ride.destinationPlace == null) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Container(
        padding: const EdgeInsets.all(8),
        color: Colors.grey.shade200,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.airline_stops, color: Colors.red),
                width5,
                Expanded(
                  child: Text(
                    "Destination changed ${ride.destinationPlace!.length} time/s",
                    style: TextStyle(
                      color: Colors.black,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    _showAllDestinations(ride.destinationPlace!);
                  },
                  child: Icon(
                    Icons.list,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            height10,
            Text(
              "Final destination is",
              style: TextStyle(
                fontSize: 10,
                color: Colors.black,
              ),
            ),
            Text(
              ride.destinationPlace![ride.destinationPlace!.length - 1].title,
              style: TextStyle(
                color: Colors.black,
              ),
            ),
            (ride.endAddress ?? "") ==
                    ride.destinationPlace![ride.destinationPlace!.length - 1]
                        .title
                ? const SizedBox()
                : Text(
                    ride.endAddress ?? "",
                    style: TextStyle(
                      color: Colors.black,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  void _showAllDestinations(List<StopsModel> destinations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("All Destinations"),
          content: Container(
            width: double.maxFinite,
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: destinations.length,
              separatorBuilder: (BuildContext context, int index) => Divider(),
              itemBuilder: (BuildContext context, int index) {
                return ListTile(
                  title: Text(destinations[index].title),
                  subtitle: Text(destinations[index].currentAdress ?? ""),
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text("Close"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showAllStops(List<StopsModel> destinations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("All Stops"),
          content: Container(
            width: double.maxFinite,
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: destinations.length,
              separatorBuilder: (BuildContext context, int index) => Divider(),
              itemBuilder: (BuildContext context, int index) {
                return ListTile(
                  title: Text(destinations[index].title),
                  subtitle: Text(destinations[index].currentAdress ?? ""),
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text("Close"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  poolRideWidget({required List<OnRideRequest> poolrides}) {
    return Stack(
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text(
                      "Pool ride",
                      style: AppTextStyles.header(),
                    ),
                    ListView.separated(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          OnRideRequest onRideRequest = poolrides[index];

                          return ValueListenableBuilder<OnRideRequest>(
                            valueListenable: _selectedOnrideRequest,
                            builder: (context, value, child) {
                              return _handlePoolRideBottomSheet(
                                  onRideRequest: onRideRequest);
                            },
                          );
                        },
                        separatorBuilder: (context, index) => height10,
                        itemCount: poolrides.length)
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        )
      ],
    );
  }

  Widget _slidingContainer({
    required OnRideRequest onRideRequest,
    required Widget child,
  }) {
    return Slidable(
      // Specify a key if the Slidable is dismissible.
      key: const ValueKey(0),

      endActionPane: ActionPane(
        extentRatio: .3,
        motion: ScrollMotion(),
        children: [
          Row(
            children: [
              width20,
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ZegoCallButton(
                    context: context,
                    riderId: onRideRequest.riderId.toString(),
                    riderName: onRideRequest.riderName!,
                  ),
                  ChatButton(
                      onRiderPlayerIdMissing: () {
                        GlobalMethods.showConfirmationDialog(
                            context: context,
                            onPositiveAction: () {
                              _getCurrentRide();
                            },
                            title: errorMessage +
                                "Please press ok and check again");
                      },
                      onRideRequest: onRideRequest)
                  // height20,
                ],
              ),
            ],
          ),
        ],
      ),

      child: InkWell(
        onTap: () {},
        child: Container(
            padding: EdgeInsets.symmetric(
                horizontal: screenPaddingValue / 2,
                vertical: screenPaddingValue / 2),
            decoration: BoxDecoration(
                border: _selectedOnrideRequest.value.id == onRideRequest.id
                    ? Border.all(width: 2, color: AppColors.greenColor)
                    : Border.all(
                        color: AppColors.blackColor(context), width: .5)),
            child: Column(
              children: [
                Align(
                    alignment: Alignment.topRight,
                    child: ChatCounter(
                      onRideRequest: onRideRequest,
                    )),
                child,
              ],
            )),
      ),
    );
  }

  Future<void> _clearMapAnnotations() async {
    for (var annotation in _mapAnnotations) {
      await _pointAnnotationManager.delete(annotation);
    }
    _mapAnnotations.clear();
  }
}

Widget permissionMessageWidget() {
  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Text(
        """To make sure you never miss a ride request, we kindly ask for "Allow all the time" background location permission. This will help us: """,
        style: AppTextStyles.LowTitle(),
      ),
    ],
  );
}

Widget mapOverlayerIfNotGranted(BuildContext context) {
  return Container(
    margin: EdgeInsets.all(16),
    padding: EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.15),
      borderRadius: BorderRadius.circular(20),
      boxShadow: [
        BoxShadow(
          // color: Colors.white.withOpacity(0.1),
          blurRadius: 100,
          spreadRadius: 3,
        ),
      ],
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "Permissions Required",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 8),
        Text(
          "To view your current location on the map, please grant the required permissions.",
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
        SizedBox(height: 24),
        AppButton(
          text: "Continue",
          onPressed: () async {
            Navigator.of(context).push<bool>(
              MaterialPageRoute(
                builder: (context) => RequiredPermissionScreen(),
              ),
            );
          },
        ),
      ],
    ),
  );
}
