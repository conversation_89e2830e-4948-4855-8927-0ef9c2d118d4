import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/foundation.dart';
import 'package:location/location.dart' as map;
import 'package:flutter_sliding_toast/flutter_sliding_toast.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:lottie/lottie.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/features/care/screens/CreateRideHelpTabScreen.dart';
import 'package:rooo_driver/features/care/screens/RideHelpdetailsScreen.dart';
import 'package:rooo_driver/features/care/screens/care_screen.dart';
import 'package:rooo_driver/features/care/screens/new_care_detail_screen.dart';
import 'package:rooo_driver/features/edit_profile/screens/edit_profile_screen.dart';
import 'package:rooo_driver/features/inbox/screens/inbox_screen2.dart';
import 'package:rooo_driver/features/opportunity/screens/opportunity_screen2.dart';
import 'package:rooo_driver/features/permissions/screens/location_permission_screen.dart';
import 'package:rooo_driver/features/registration/screens/complete_profile_screen.dart';
import 'package:rooo_driver/features/vehicles/screens/vehicle_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/screens/ChatScreen.dart';
import 'dart:ui' as ui;
import '../../features/Refferals/screens/refferals_main_screen.dart';
import 'local_waiting_time_model.dart';

class GlobalMethods {
  static closeScreen(BuildContext context) {
    Navigator.pop(context);
  }

  static removeSavedScreen() {
    sharedPref.remove("CURRENT_SCREEN");
  }

  static showAdvertismentDialog(
      {required BuildContext context,
      required void Function() positiveAction,
      void Function()? negativeAction,
      Function()? onShowedDialog,
      bool? barrierDismissible,
      required String title,
      String? positiveText,
      required String imageUrl,
      String? negativeText}) {
    if (onShowedDialog != null) {
      onShowedDialog();
    }
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: barrierDismissible ?? true,
          child: AlertDialog(
            title: Column(
              children: [
                InkWell(
                    onTap: () {
                      closeScreen(context);
                      positiveAction();
                    },
                    child: Stack(
                      alignment: Alignment.topRight,
                      children: [
                        commonCachedNetworkImage(imageUrl),
                        InkWell(
                          onTap: () {
                            closeScreen(context);
                          },
                          child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(.5),
                                  shape: BoxShape.circle),
                              child: Icon(
                                Icons.close,
                                color: AppColors.whiteColor(context),
                              )),
                        )
                      ],
                    )),
                Text(
                  title,
                  style: AppTextStyles.title(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<T> showConfirmationDialog<T>({
    required BuildContext context,
    required void Function() onPositiveAction,
    Widget? child,
    void Function()? onNegativeAction,
    Function()? onShowedDialog,
    bool? barrierDismissible,
    String? title,
    String? lottieImage,
    String? positiveText,
    String? negativeText,
    bool upDownButtons = false,
  }) async {
    if (onShowedDialog != null) {
      onShowedDialog();
    }
    T v = await showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: barrierDismissible ?? true,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(color: Colors.grey),
            ),
            titlePadding: EdgeInsets.zero,
            backgroundColor: Theme.of(context).brightness == Brightness.dark
                ? Colors.black
                : Colors.white,
            title: Container(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade600
                    : Colors.grey.shade200,
                height: 90,
                width: double.infinity,
                child: Lottie.asset("assets/lottie/alert.json")),
            content: Padding(
              padding: const EdgeInsets.all(6),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  title == null
                      ? SizedBox()
                      : Text(
                          title,
                          style: TextStyle(
                            fontSize: 14,
                            // color: Colors.black,
                          ),
                        ),
                  height20,
                  child == null ? SizedBox() : child,
                  height20,
                  upDownButtons
                      ? Column(
                          children: [
                            FilledButton(
                                onPressed: () {
                                  if (onNegativeAction != null) {
                                    onNegativeAction();
                                  }
                                  Navigator.of(context).pop(false);
                                },
                                child: Text(negativeText ?? "Cancel",
                                    style: TextStyle())),
                            height10,
                            FilledButton(
                                onPressed: () {
                                  onPositiveAction();
                                  Navigator.of(context).pop(true);
                                },
                                child: Text(positiveText ?? "Ok")),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            FilledButton(
                                onPressed: () {
                                  if (onNegativeAction != null) {
                                    onNegativeAction();
                                  }
                                  Navigator.of(context).pop(false);
                                },
                                child: Text(negativeText ?? "Cancel",
                                    style: TextStyle())),
                            FilledButton(
                                onPressed: () {
                                  onPositiveAction();
                                  Navigator.of(context).pop(true);
                                },
                                child: Text(positiveText ?? "Ok")),
                          ],
                        ),
                ],
              ),
            ),
          ),
        );
      },
    );

    return v;
  }

  static void toast(String? value,
      {Color? bgColor, Color? textColor, bool print = false}) {
    if (value!.isEmpty || (!kIsWeb && Platform.isLinux)) {
      log(value);
    } else {
      Fluttertoast.showToast(
          msg: value,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.white,
          textColor: Colors.black,
          fontSize: 16.0);
      if (print) log(value);
    }
  }

  static showSuccessDialog({
    required BuildContext context,
    required void Function() positiveAction,
    required String title,
    String buttonText = "OK",
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Container(
                color: Colors.grey.shade200,
                height: 90,
                child: Lottie.asset("assets/lottie/success.json")),
            contentPadding: EdgeInsets.zero,
            titlePadding: EdgeInsets.zero,
            content: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      // color: Colors.black,
                    ),
                  ),
                  height20,
                  FilledButton(
                    onPressed: () {
                      positiveAction();
                    },
                    child: Text(buttonText),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static showOnlineOfflineDialog(
      {required BuildContext context,
      required void Function() positiveAction,
      void Function()? negativeAction,
      required Widget title,
      Function()? onShowedDialog,
      String? lottieImage,
      bool? barrierDismissible,
      String? positiveText,
      String? negativeText}) {
    if (onShowedDialog != null) {
      onShowedDialog();
    }
    showDialog(
      barrierDismissible: barrierDismissible ?? true,
      context: context,
      builder: (context) {
        return PopScope(
          canPop: barrierDismissible ?? true,
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.85,
              padding: EdgeInsets.zero,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 100,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(10)),
                    ),
                    child: Lottie.asset(
                      lottieImage ?? "assets/lottie/alert.json",
                      fit: BoxFit.contain,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        title,
                        SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (barrierDismissible ?? true)
                              Expanded(
                                child: FilledButton(
                                  onPressed: () {
                                    if (negativeAction != null) {
                                      closeScreen(context);
                                      negativeAction();
                                    } else {
                                      closeScreen(context);
                                    }
                                  },
                                  child: Text(negativeText ?? "Cancel"),
                                ),
                              ),
                            if (barrierDismissible ?? true) SizedBox(width: 16),
                            Expanded(
                              child: FilledButton(
                                onPressed: () {
                                  closeScreen(context);
                                  positiveAction();
                                },
                                child: Text(positiveText ?? "Ok"),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static showInfoDialog(
      {required BuildContext context,
      required void Function() positiveAction,
      void Function()? negativeAction,
      required String title,
      Function()? onShowedDialog,
      bool? barrierDismissible,
      String? positiveText,
      String? lottieImage,
      String? negativeText}) {
    if (onShowedDialog != null) {
      onShowedDialog();
    }
    showDialog(
      barrierDismissible: barrierDismissible ?? true,
      context: context,
      builder: (context) {
        return PopScope(
          canPop: barrierDismissible ?? true,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(color: Colors.grey),
            ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    height: 150,
                    child: Lottie.asset(
                        lottieImage ?? "assets/lottie/alert.json")),
                Text(
                  title,
                  style: AppTextStyles.title(),
                ),
                height20,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Visibility(
                        visible: barrierDismissible ?? true,
                        child: Expanded(
                          child: FilledButton(
                              onPressed: () {
                                if (negativeAction != null) {
                                  closeScreen(context);
                                  negativeAction();
                                } else {
                                  closeScreen(context);
                                }
                              },
                              child: Text(negativeText ?? "Cancel")),
                        )),
                    Visibility(
                        visible: barrierDismissible ?? true, child: width10),
                    Expanded(
                      child: FilledButton(
                          onPressed: () {
                            closeScreen(context);
                            positiveAction();
                          },
                          child: Text(positiveText ?? "Ok")),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<T> showInfoDialogNew<T>({
    required BuildContext context,
    required void Function() onClick,
    required String title,
    bool barrierDismissible = false,
    String buttonText = "Ok",
  }) async {
    var t = await showDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (context) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return PopScope(
          canPop: barrierDismissible,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(
                  color: isDark ? Colors.grey.shade700 : Colors.grey),
            ),
            titlePadding: EdgeInsets.zero,
            backgroundColor: isDark ? Colors.black : Colors.white,
            title: Container(
                color: isDark ? Colors.grey.shade800 : Colors.grey.shade200,
                height: 90,
                width: double.infinity,
                child: Lottie.asset("assets/lottie/alert.json")),
            content: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.white : Colors.black,
                    ),
                  ),
                  height20,
                  Center(
                    child: FilledButton(
                      onPressed: () {
                        onClick();
                      },
                      child: Text(
                        buttonText,
                        style: TextStyle(
                          color: isDark ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
    return t;
  }

  static dynamic showActivity({
    required BuildContext context,
    required String title,
    List<BuildContext>? newContext,
  }) async {
    var gh = showDialog(
      barrierLabel: "Activity",
      barrierDismissible: false,
      barrierColor: Colors.grey.shade600.withValues(alpha: 0.5),
      context: context,
      builder: (context) {
        if (newContext != null) {
          newContext.add(context);
        }
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return PopScope(
          canPop: false,
          child: AlertDialog(
            titlePadding: EdgeInsets.zero,
            backgroundColor: isDark ? Colors.black : Colors.white,
            content: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Center(
                    child: CircularProgressIndicator(
                      color: isDark ? Colors.white : Colors.black,
                    ),
                  ),
                  height20,
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.white : Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
    return gh;
  }

  static Future<void> animateMap({
    required Completer<GoogleMapController> completer,
    required LatLng location,
    required double? zoom,
  }) async {
    final GoogleMapController controller = await completer.future;
    controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(target: location, zoom: zoom ?? 17.0),
      ),
    );
  }

  static Future<Uint8List> getBytesFromAsset(
      String path, BuildContext context) async {
    double pixelRatio = MediaQuery.of(context).devicePixelRatio;
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: pixelRatio.round() * 50);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  static succesToast(BuildContext context, String message) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return InteractiveToast.slideSuccess(
      context,
      title: Text(
        message,
        style: AppTextStyles.title(
            color: isDark ? Colors.white : AppColors.primaryBlackColor),
      ),
      toastSetting: SlidingToastSetting(
        displayDuration: Duration(seconds: 2),
        curve: Curves.fastLinearToSlowEaseIn,
        toastAlignment: Alignment.topCenter,
        toastStartPosition: ToastPosition.top,
      ),
      toastStyle: ToastStyle(
        boxShadow: [
          BoxShadow(
            color: (isDark ? Colors.green : Colors.green).withAlpha(25),
            blurRadius: 5,
            spreadRadius: 3,
            offset: const Offset(2, 2),
          ),
        ],
        backgroundColor: isDark ? const Color(0xFF003300) : Colors.white,
      ),
    );
  }

  static errorToast(BuildContext context, String message, {int seconds = 3}) {
    if (message == errorMessage) {
      return;
    }
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return InteractiveToast.slideError(
      context,
      title: Text(
        message,
        style: AppTextStyles.title(color: isDark ? Colors.white : Colors.red),
      ),
      toastSetting: SlidingToastSetting(
        displayDuration: Duration(seconds: seconds),
        curve: Curves.fastLinearToSlowEaseIn,
        toastAlignment: Alignment.topCenter,
        toastStartPosition: ToastPosition.top,
      ),
      toastStyle: ToastStyle(
        boxShadow: [
          BoxShadow(
            color: Colors.red.withAlpha(25),
            blurRadius: 5,
            spreadRadius: 3,
            offset: const Offset(2, 2),
          ),
        ],
        backgroundColor: isDark ? const Color(0xFF330000) : Colors.white,
      ),
    );
  }

  static infoToast(
    BuildContext context,
    String message,
  ) {
    if (message == errorMessage) {
      return;
    }
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return InteractiveToast.slide(
      context,
      title: Row(
        children: [
          Expanded(
            child: Text(
              message,
              style: AppTextStyles.title(
                  color:
                      isDark ? Colors.white : AppColors.primaryColor(context)),
            ),
          ),
          SizedBox(width: 8),
          Icon(Icons.info_outline,
              color: isDark ? Colors.white : Colors.grey[800]),
        ],
      ),
      toastSetting: SlidingToastSetting(
        curve: Curves.fastLinearToSlowEaseIn,
        toastAlignment: Alignment.topCenter,
        toastStartPosition: ToastPosition.top,
      ),
      toastStyle: ToastStyle(
        boxShadow: [
          BoxShadow(
            color: (isDark ? Colors.grey : Colors.grey).withAlpha(25),
            blurRadius: 5,
            spreadRadius: 3,
            offset: const Offset(2, 2),
          ),
        ],
        backgroundColor: isDark ? const Color(0xFF222222) : Colors.white,
      ),
    );
  }

  static Future<T?> replaceScreen<T>(
      {required BuildContext context,
      required dynamic screen,
      required ScreenIdentifier screenIdentifier}) async {
    sharedPref.setString("CURRENT_SCREEN", screenIdentifier.toString());

    return await Navigator.of(context).pushReplacement(MaterialPageRoute(
      builder: (context) => screen,
      settings: RouteSettings(name: screenIdentifier.name),
    ));
  }

  static Future<T?> pushScreen<T>(
      {required BuildContext context,
      required dynamic screen,
      required ScreenIdentifier screenIdentifier}) async {
    sharedPref.setString("CURRENT_SCREEN", screenIdentifier.toString());

    return await Navigator.of(context).push(MaterialPageRoute(
      builder: (context) => screen,
      settings: RouteSettings(name: screenIdentifier.name),
    ));
  }

  static Future<T?> pushAndRemoveAll<T>(
      {required BuildContext context,
      required dynamic screen,
      required ScreenIdentifier screenIdentifier}) async {
    sharedPref.setString("CURRENT_SCREEN", screenIdentifier.toString());

    return await Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => screen,
          settings: RouteSettings(name: screenIdentifier.name),
        ),
        (Route<dynamic> route) => false);
  }

  static Future<bool> isCurrentLocationInDesiredArea(
      {required LatLng currentLocation, required LatLng targetLocation}) async {
    num distanceinKms = Geolocator.distanceBetween(
          currentLocation.latitude,
          currentLocation.longitude,
          targetLocation.latitude,
          targetLocation.longitude,
        ) /
        1000;

    return distanceinKms <= (GlobalState.appSettingModel?.allCommonGeofencing ?? 1);
  }

  static Future<bool> checkImageSize(
      {required XFile result, required BuildContext context}) async {
    final int fileSizeInBytes = await result.length();

    final double fileSizeInMB = fileSizeInBytes / (1024 * 1024);

    if (fileSizeInMB > 5) {
      GlobalMethods.showInfoDialog(
          context: context,
          positiveAction: () {},
          title: "Please upload image less than 5 MB");
      return false;
    } else {
      return true;
    }
  }

  static String handleFirebaseError(dynamic error) {
    String errorMessage;

    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          errorMessage = 'No user found for that email.';
          break;
        case 'wrong-password':
          errorMessage = 'Wrong password provided for that user.';
          break;
        case 'email-already-in-use':
          errorMessage = 'The account already exists for that email.';
          break;
        default:
          errorMessage =
              'An unknown FirebaseAuth error occurred: ${error.message}';
          break;
      }
    } else if (error is FirebaseException) {
      errorMessage = 'Firebase error: ${error.message}';
    } else if (error is Exception) {
      errorMessage = 'General exception: ${"Server error"}';
    } else {
      errorMessage = 'Unknown error occurred.';
    }
    return errorMessage;
  }

  static Future<void> handleNotification(
      OSNotification notification, bool fromTerminated) async {
    print('\n=== Notification Received ===');
    print('Time: ${DateTime.now()}');
    print('Title: ${notification.title}');
    print('Body: ${notification.body}');
    print('Data: ${notification.additionalData}');
    print('App State: ${fromTerminated ? "Terminated" : "Foreground"}');
    print('==========================\n');

    bool _isTopMostScreen(ScreenIdentifier screenIdentifier) {
      String localString = sharedPref.getString("CURRENT_SCREEN") ?? "";
      String screenName = screenIdentifier.toString();
      return screenName == localString;
    }

    if (notification.additionalData?['type'] ==
        NotificationType.ride_alert.name) {
      print('Handling ride alert notification');
      if (!fromTerminated) {
        GlobalMethods.showInfoDialog(
            context: navigatorKey.currentContext!,
            positiveAction: () {},
            title: (notification.title ?? '') +
                '\n\n' +
                (notification.body ?? ''));
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.chat_msg.name) {
      notification.additionalData?['ride_id'];

      print("chatmmmm");
      int rideId = int.parse(notification.additionalData?['ride_id']);

      String uid = notification.additionalData?['uid'];
      String riderFirestoreId = notification.additionalData?['firestore_id'];

      String playerId = notification.additionalData?['player_id'];
      String name = notification.additionalData?['name'];
      String profileImage = notification.additionalData?['profile_image'];
      GlobalState.chat_count.value
          .firstWhere(
            (element) => element.rideId == rideId,
            orElse: () => ChatCountModel(rideId: -1, chatCount: 0),
          )
          .chatCount++;

      log(GlobalState.chat_count.value);
      GlobalState.chat_count.notifyListeners();

      if (fromTerminated) {
        if (GlobalState.current_ride?.value.onRideRequest != null) {
          if (!_isTopMostScreen(ScreenIdentifier.chatScreen)) {
            bool? result = await GlobalMethods.pushScreen(
                context: navigatorKey.currentContext!,
                screen: ChatScreen(
                    receiverFirestoreId: riderFirestoreId.toString(),
                    rideId: rideId,
                    uid: uid,
                    playerId: playerId,
                    riderName: name,
                    riderProfileImage: profileImage),
                screenIdentifier: ScreenIdentifier.chatScreen);

            if (result == null) {
              List<ChatCountModel> list = GlobalState.chat_count.value
                  .where((element) => element.rideId == rideId)
                  .toList();

              if (list.isNotEmpty) {
                list[0].chatCount = 0;
              }

              GlobalState.chat_count.notifyListeners();
              ;
            }
          } else {
            if (GlobalState.chatRideId != rideId) {
              GlobalMethods.replaceScreen(
                  context: navigatorKey.currentContext!,
                  screen: ChatScreen(
                    rideId: rideId,
                    uid: uid,
                    playerId: playerId,
                    riderName: name,
                    riderProfileImage: profileImage,
                    receiverFirestoreId: riderFirestoreId,
                  ),
                  screenIdentifier: ScreenIdentifier.chatScreen);
            }
          }
        } else {
          GlobalMethods.infoToast(
              navigatorKey.currentContext!, "no active ride");
        }
      }
    } else if (notification.additionalData?['type'] ==
        rideQuietPeiodNotification) {
      if (!fromTerminated) {
        GlobalMethods.showConfirmationDialog(
            context: navigatorKey.currentContext!,
            onPositiveAction: () {},
            title: (notification.title ?? '') +
                '\n\n' +
                (notification.body ?? ''));
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.ride_start.name) {
      if (!fromTerminated) {
        GlobalMethods.showInfoDialog(
            context: navigatorKey.currentContext!,
            positiveAction: () {},
            title: (notification.title ?? '') +
                '\n\n' +
                (notification.body ?? ''));
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.ride_near_start.name) {
      if (!fromTerminated) {
        GlobalMethods.showInfoDialog(
            context: navigatorKey.currentContext!,
            positiveAction: () {},
            title: (notification.title ?? '') +
                '\n\n' +
                (notification.body ?? ''));
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.driver_profile_update.name) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: EditProfileScreen(isFromDahboard: false),
            screenIdentifier: ScreenIdentifier.DocumentScreen);
      }
    } else if (notification.additionalData?['type'] ==
            NotificationType.vehicle_document.name ||
        notification.additionalData?['type'] ==
            NotificationType.vehicle_registration_expiring_soon.name ||
        notification.additionalData?['type'] ==
            NotificationType.vehicle_inspection_expiring_soon.name ||
        notification.additionalData?['type'] ==
            NotificationType.vehicle_inspection_rejected.name ||
        notification.additionalData?['type'] ==
            NotificationType.vehicle_inspection_rejected.name) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: VehiclesScreen(),
            screenIdentifier: ScreenIdentifier.DocumentScreen);
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.new_inbox.name) {
      GlobalState.global_inbox_count.value =
          GlobalState.global_inbox_count.value + 1;
      BlocProvider.of<InboxCubit>(navigatorKey.currentContext!)
          .getInbox(current_page: 1);

      if (fromTerminated) {
        if (!_isTopMostScreen(ScreenIdentifier.inboxScreen)) {
          // Navigator.of(navigatorKey.currentContext!)
          //     .push(_getPageRoute("InboxScreen2"));
          GlobalMethods.pushScreen(
              context: navigatorKey.currentContext!,
              screen: InboxScreen(),
              screenIdentifier: ScreenIdentifier.inboxScreen);
          // launchScreen(null, InboxScreen2(),
          //     screenIdentifier: ScreenIdentifier.inboxScreen);
        }
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.new_offer.name) {
      if (fromTerminated) {
        if (!_isTopMostScreen(ScreenIdentifier.referralScreen)) {
          // Navigator.of(navigatorKey.currentContext!)
          //     .push(_getPageRoute("ReferralScreen"));
          GlobalMethods.pushScreen(
              context: navigatorKey.currentContext!,
              screen: ReferralScreen(
                screenIndex: 2,
              ),
              screenIdentifier: ScreenIdentifier.referralScreen);
        }
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.new_referrals.name) {
      if (fromTerminated) {
        if (!_isTopMostScreen(ScreenIdentifier.referralScreen)) {
          GlobalMethods.pushScreen(
              context: navigatorKey.currentContext!,
              screen: ReferralScreen(
                screenIndex: 0,
              ),
              screenIdentifier: ScreenIdentifier.referralScreen);
        }
      }
    } else if (notification.additionalData?['type'] ==
        NotificationType.waiting_time.name) {
    } else if (notification.additionalData?["type"] == "carecomment") {
      GlobalState.global_care_count.value =
          GlobalState.global_care_count.value + 1;

      if (fromTerminated) {
        if (!_isTopMostScreen(ScreenIdentifier.careScreen)) {
          // Navigator.of(navigatorKey.currentContext!)
          //     .push(_getPageRoute("NewCareScreen"));
          GlobalMethods.pushScreen(
              context: navigatorKey.currentContext!,
              screen: NewCareScreen(isPendingCare: true),
              screenIdentifier: ScreenIdentifier.careScreen);
          // launchScreen(
          //   null,
          //   NewCareScreen(
          //     isPendingCare: true,
          //   ),
          // );
        }
      }
      // launchScreen(
      //   null,
      //   NewCareScreen(
      //     isPendingCare: true,
      //   ),
      // );
    } else if (notification.additionalData?["type"] ==
            NotificationType.document_near_expire.name ||
        notification.additionalData?["type"] ==
            NotificationType.document_rejected.name) {
      print('\n=== Document Notification Handling ===');
      print('Type: ${notification.additionalData?["type"]}');
      print('Current Screen: ${sharedPref.getString("CURRENT_SCREEN")}');

      if (fromTerminated) {
        print('App was terminated, navigating to document screen');
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: DocumentScreen(canGoToDashboard: false),
            screenIdentifier: ScreenIdentifier.DocumentScreen);
      } else {
        print('App is in foreground, showing dialog');
        GlobalMethods.showInfoDialog(
            context: navigatorKey.currentContext!,
            positiveAction: () {},
            title: (notification.title ?? '') +
                '\n\n' +
                (notification.body ?? ''));
      }
      print('=== End Document Notification Handling ===\n');
    } else if (notification.additionalData?["type"] ==
        NotificationType.new_opportunity.name) {
      GlobalState.global_opportunity_count.value =
          GlobalState.global_opportunity_count.value + 1;
      if (fromTerminated) {
        if (!_isTopMostScreen(ScreenIdentifier.newOpportunityScreen)) {
          GlobalMethods.pushScreen(
              context: navigatorKey.currentContext!,
              screen: OpportunityScreen(),
              screenIdentifier: ScreenIdentifier.newOpportunityScreen);
        }
      }
    } else if (notification.additionalData?["type"] ==
        NotificationType.document_expire.name) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: DocumentScreen(canGoToDashboard: false),
            screenIdentifier: ScreenIdentifier.DocumentScreen);
      }
    } else if (notification.additionalData?["type"] ==
            NotificationType.document_near_expire.name ||
        notification.additionalData?["type"] ==
            NotificationType.document_rejected.name) {
      print(
          'Received document notification: ${notification.additionalData?["type"]}');
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: DocumentScreen(canGoToDashboard: false),
            screenIdentifier: ScreenIdentifier.DocumentScreen);
      } else {
        // Show notification even if app is in foreground
        GlobalMethods.showInfoDialog(
            context: navigatorKey.currentContext!,
            positiveAction: () {},
            title: (notification.title ?? '') +
                '\n\n' +
                (notification.body ?? ''));
      }
    } else if (notification.additionalData?["type"] ==
        NotificationType.subscription_expiring_soon.name) {
      GlobalMethods.showInfoDialogNew(
        context: navigatorKey.currentContext!,
        onClick: () {
          closeScreen(navigatorKey.currentContext!);
        },
        title: notification.body ?? "Server error occured",
      );
    } else if (notification.additionalData?["type"] ==
        NotificationType.subscription_expired.name) {
      GlobalMethods.showInfoDialogNew(
        context: navigatorKey.currentContext!,
        onClick: () {
          closeScreen(navigatorKey.currentContext!);
        },
        title: notification.body ?? "Server error occured",
      );
    } else if (notification.additionalData?["type"] == "document_approved") {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: DocumentScreen(canGoToDashboard: false),
            screenIdentifier: ScreenIdentifier.DocumentScreen);
      }
    } else if (notification.additionalData?["type"] == "document_approved") {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: DocumentScreen(canGoToDashboard: false),
            screenIdentifier: ScreenIdentifier.DocumentScreen);
      }
    } else if (notification.additionalData?["type"] ==
        "profile_photo_incomplete") {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: EditProfileScreen(
              isFromDahboard: false,
            ),
            screenIdentifier: ScreenIdentifier.EditProfileScreen);
      }
    } else if (notification.additionalData?["type"] ==
        NotificationType.carecomment) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );

        int careId = int.tryParse(
                (notification.additionalData?['complaint_id'] ?? 0)
                    .toString()) ??
            0;
        bool isClosed = notification.additionalData?['is_closed'] ?? false;
        if (careId == 0) {
          return;
        }

        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: NewCareDetailsScreen(
              id: careId,
              isClosed: isClosed,
            ),
            screenIdentifier: ScreenIdentifier.EditProfileScreen);
      }
    } else if (notification.additionalData?["type"] ==
        NotificationType.complaintcomment) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );

        int issueId = int.tryParse(
                (notification.additionalData?['issue_id'] ?? 0).toString()) ??
            0;
        bool isClosed = notification.additionalData?['is_closed'] ?? false;
        if (issueId == 0) {
          return;
        }

        GlobalMethods.pushScreen(
            context: navigatorKey.currentContext!,
            screen: RideHelpDetailsScreen(
              helpData: RideHelpData(
                id: issueId,
                booking_date: "booking_date",
                booking_from: "booking_from",
                booking_to: "booking_to",
                created_at: "created_at",
                help_count: 0,
                ride_request_id: 0,
                subject: "subject",
                description: "description",
                status: "status",
              ),
              isClosed: isClosed,
              cardStateUpdater: () {},
            ),
            screenIdentifier: ScreenIdentifier.EditProfileScreen);
      }
    }
  }

  static Future stopServerTracking() async {
    try {
      GlobalState.driver_server_timer?.cancel();
      GlobalState.driver_server_timer = null;
      await appLocation.enableBackgroundMode(enable: false);

      await appLocationStream?.cancel();
      appLocationStream = null;
    } catch (e) {}
  }

  static startServerBackgroundTracking() {
    try {
      appLocation.changeSettings(interval: 20);
      appLocation.enableBackgroundMode(enable: true);

      appLocationStream?.cancel();
      appLocationStream = null;

      appLocationStream = appLocation.onLocationChanged
          .listen((map.LocationData currentLocation) async {
        await update_driver_position_at_serverBG(
            sharedPrefBG: sharedPref,
            location:
                LatLng(currentLocation.latitude!, currentLocation.longitude!));
      });
    } catch (e) {}
  }

  static Future startsevertrackingTracking() async {
    await checkLocationPermission(onPermissionGranted: () async {
      await stopServerTracking();

      if (GlobalState.is_foreground_state) {
        Position location = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
        GlobalState.driverPosition =
            LatLng(location.latitude, location.longitude);
        await sharedPref.reload();
        await update_driver_position_at_serverBG(
          sharedPrefBG: sharedPref,
          location: GlobalState.driverPosition!,
        );
        GlobalState.driver_server_timer?.cancel();
        GlobalState.driver_server_timer = null;
        GlobalState.driver_server_timer =
            Timer.periodic(Duration(seconds: 10), (timer) async {
          Position location = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high,
          );
          GlobalState.driverPosition =
              LatLng(location.latitude, location.longitude);

          await sharedPref.reload();
          await update_driver_position_at_serverBG(
            sharedPrefBG: sharedPref,
            location: GlobalState.driverPosition!,
          );
        });
      } else {
        await startServerBackgroundTracking();
      }
    });
  }

  static checkCameraPermission(
      {required Function onSuccess, required BuildContext context}) async {
    PermissionStatus permissionStatus = await Permission.camera.status;
    if (permissionStatus.isDenied) {
      permissionStatus = await Permission.camera.request();
    }
    if (permissionStatus.isPermanentlyDenied) {
      await showInfoDialog(
          context: context,
          positiveAction: () async {
            await openAppSettings();
          },
          title: language.cameraAccesMessage);
    }
    if (permissionStatus.isGranted) {
      onSuccess();
    }
  }

  static Future checkZegoCallPermission(
      {required Function onSuccess, required BuildContext context}) async {
    PermissionStatus permissionStatus = await Permission.microphone.status;
    if (permissionStatus.isDenied || permissionStatus.isPermanentlyDenied) {
      permissionStatus = await Permission.microphone.request();
    }
    if (permissionStatus.isPermanentlyDenied || permissionStatus.isDenied) {
      showConfirmationDialog(
          context: context,
          onPositiveAction: () async {
            await openAppSettings();
          },
          title: language.microphoneAccessMessage);
      // await showInfoDialog(context, title: language.microphoneAccessMessage,
      //     onAccept: (v) async {
      //   closeScreen(context);
      //   await openAppSettings();
      // });
    }
    if (permissionStatus.isGranted) {
      onSuccess();
    }
  }

  static Future saveResponseLoccally({required UserData data}) async {
    await sharedPref.setString(TOKEN, data.apiToken.validate());
    await sharedPref.setString(USER_TYPE, data.userType.validate());
    await sharedPref.setString(FIRST_NAME, data.firstName.validate());
    await sharedPref.setString(LAST_NAME, data.lastName.validate());
    await sharedPref.setBool(
        IS_PROFILE_COMPLETE, data.isProfileComplete ?? true);
    await sharedPref.setString(CONTACT_NUMBER, data.contactNumber.validate());
    await sharedPref.setString(USER_EMAIL, data.email.validate());
    await sharedPref.setInt(USER_ID, data.id ?? 0);
    await sharedPref.setString(
        USER_PROFILE_PHOTO, data.profileImage.validate());
    await sharedPref.setString(GENDER, data.gender.validate());
    await sharedPref.setString(
        OTHER_GENDER_TEXT, data.otherGenderText.validate());
    if (data.isOnline != null)
      await sharedPref.setInt(IS_ONLINE, data.isOnline ?? 0);
    await sharedPref.setInt(IS_Verified_Driver, data.isVerifiedDriver ?? 0);
    if (data.uid != null) await sharedPref.setString(UID, data.uid.validate());
    await sharedPref.setString(LOGIN_TYPE, data.email.validate());

    await appStore.setLoggedIn(true);
    await appStore.setUserEmail(data.email.validate());
    await appStore.setUserProfile(data.profileImage.validate());

    // if (sharedPref.getInt(IS_Verified_Driver) == 1) {
    //   launchScreen(context, InitialScreen(), isNewTask: true);
    // } else {
    //   launchScreen(
    //       context, VerifyDeliveryPersonScreen(isShow: true),
    //       pageRouteAnimation: PageRouteAnimation.Slide,
    //       isNewTask: true);
    // }
  }

  static Future saveDataLocally({required UserData user}) async {
    email_verified_at = user.email_verified_at;

    await sharedPref.setString(TOKEN, user.apiToken.validate());
    await sharedPref.setString(USER_TYPE, user.userType.validate());
    await sharedPref.setString(FIRST_NAME, user.firstName.validate());
    await sharedPref.setString(LAST_NAME, user.lastName.validate());
    await sharedPref.setString(CONTACT_NUMBER, user.contactNumber.validate());
    await sharedPref.setString(USER_EMAIL, user.email.validate());
    await sharedPref.setString(REFFERALCODE, user.referralCode.validate());
    await sharedPref.setString(UID, user.uid.validate());
    await sharedPref.setBool(
        IS_PROFILE_COMPLETE, user.isProfileComplete ?? true);
    await sharedPref.setInt(USER_ID, user.id ?? 0);
    await sharedPref.setString(
        USER_PROFILE_PHOTO, user.profileImage.validate());
    await sharedPref.setString(GENDER, user.gender.validate());
    await sharedPref.setString(
        OTHER_GENDER_TEXT, user.otherGenderText.validate());
    if (user.isOnline != null)
      await sharedPref.setInt(IS_ONLINE, user.isOnline ?? 0);
    await sharedPref.setInt(IS_Verified_Driver, user.isVerifiedDriver ?? 0);
    if (user.uid != null) await sharedPref.setString(UID, user.uid.validate());
    await sharedPref.setString(LOGIN_TYPE, user.loginType.validate());

    await appStore.setLoggedIn(true);
    await appStore.setUserEmail(user.email.validate());
    await appStore.setUserProfile(user.profileImage.validate());
  }

  static Future saveRideIdLocally(RideModel ride) async {
    List<String> rideIdsString = [];
    if (ride.pool_rides != null) {
      ride.pool_rides?.forEach((element) {
        if (!rideIdsString.contains(element.id)) {
          rideIdsString.add(element.id.toString());
        }
      });
    } else {
      if (ride.onRideRequest != null) {
        rideIdsString = [ride.onRideRequest!.id.toString()];
      }
    }

    await sharedPref.setStringList(RIDE_IDS, rideIdsString);
  }

  static Future<List<int>> getRideIdLocally() async {
    List<String> rideIdsString = await sharedPref.getStringList(RIDE_IDS) ?? [];

    return rideIdsString.map((element) => int.parse(element)).toList();
  }

  static Future deletRideIdLocally(String id) async {
    List<String> rideIdsString = await sharedPref.getStringList(RIDE_IDS) ?? [];
    rideIdsString.removeWhere((element) => element == id);
    sharedPref.setStringList(RIDE_IDS, rideIdsString);
  }

  // static Future checkNotificationPermission(
  //     {required Function() onPermissionGranted,
  //     required BuildContext context}) async {
  //   Future<PermissionStatus> requestNotificationPermission(
  //       BuildContext context) async {
  //     // final notificationstatus = await Permission.notification.status;
  //     final status = await Permission.notification.request();

  //     if (status == PermissionStatus.granted) {
  //       // Permission is already granted
  //       return status;
  //     }

  //     if (status == PermissionStatus.denied) {
  //       // Permission is denied, request permission
  //       PermissionStatus status = await GlobalMethods.pushScreen(
  //           context: context,
  //           screen: NotificationPermissionScreen(),
  //           screenIdentifier: ScreenIdentifier.locaionDenied);
  //       return status;

  //       // PermissionStatus result = await GlobalMethods.pushScreen(
  //       //       context: context,
  //       //       screen: NotificationDeniedScreen(),
  //       //       screenIdentifier: ScreenIdentifier.locaionDenied);

  //       //       return result;

  //       // if (result == PermissionStatus.granted) {
  //       //   // Permission granted after request
  //       //   return status;
  //     }

  //     if (status == PermissionStatus.permanentlyDenied) {
  //       // Permission is permanently denied, show an alert to guide the user
  //       PermissionStatus status = await GlobalMethods.pushScreen(
  //           context: context,
  //           screen: Notif(),
  //           screenIdentifier: ScreenIdentifier.locaionDenied);
  //       return status;
  //     }

  //     if (status == PermissionStatus.restricted) {
  //       // Permission is restricted, show an alert
  //       PermissionStatus status = await GlobalMethods.pushScreen(
  //           context: context,
  //           screen: NotificationDeniedScreen(),
  //           screenIdentifier: ScreenIdentifier.locaionDenied);
  //       return status;
  //     }

  //     return PermissionStatus.denied;
  //   }

  //   PermissionStatus status = await requestNotificationPermission(context);

  //   if (status == PermissionStatus.granted) {
  //     onPermissionGranted();
  //     // await initNotifications();
  //   } else {
  //     await checkNotificationPermission(
  //         onPermissionGranted: onPermissionGranted, context: context);
  //   }
  // }

  static checkLocationPermission(
      {required Function() onPermissionGranted}) async {
    Future<PermissionStatus> requestLocationPermission() async {
      PermissionStatus status = await Permission.locationAlways.status;

      if (status == PermissionStatus.granted) {
        // Permission is already granted
        return status;
      } else {
        // Permission is permanently denied, show an alert to guide the user

        // PermissionStatus status = await Permission.location.request();
        PermissionStatus status;

        // if (Platform.isAndroid) {
        status = await Permission.locationWhenInUse.request();
        if (status == PermissionStatus.granted) {
          await showInfoDialog(
              positiveText: "Ok",
              context: navigatorKey.currentContext!,
              positiveAction: () {},
              barrierDismissible: false,
              title:
                  "ROOO Driver needs to access your location to provide you with the best services. Please allow access to your location \"ALL THE TIME\".");

          status = await Permission.locationAlways.request();
        }
        // } else {
        // status = await Permission.locationAlways.request();
        // }
        if (status != PermissionStatus.granted) {
          status = await GlobalMethods.pushScreen(
              context: navigatorKey.currentContext!,
              screen: LocationPermissionScreen(),
              screenIdentifier: ScreenIdentifier.locaionDenied);
          return status;
        }
        return status;
      }
    }

    PermissionStatus status = await requestLocationPermission();

    if (status == PermissionStatus.granted) {
      onPermissionGranted();
      // await checkNotificationPermission();
      // _updatePlayerId();
      // await _startDeviceTracking();

      // _mqttForUser();
    } else {
      await checkLocationPermission(
        onPermissionGranted: onPermissionGranted,
      );
    }
  }

  static Future removerStorageForWaitinfTime() async {
    await sharedPref.remove("IS_IN_PROGRESS");
    await sharedPref.remove("serverLocations");

    await sharedPref.remove(RIDE_IDS);

    await sharedPref.remove(
      "waitingTimeList",
    );
    await sharedPref.remove("isWaitingTimeStarted");
    await sharedPref.remove("lastLocation");
    await sharedPref.remove("newLocation");
  }

  static handleInCompleteProfile(
      {required BuildContext context,
      required bool is_profile_completed,
      required void Function() positiveAction}) {
    if (!is_profile_completed) {
      showInfoDialog(
          context: context,
          positiveText: 'Ok',
          positiveAction: () {
            pushScreen(
                context: context,
                screen: CompleteProfileScreen(
                  first_name: sharedPref.getString(FIRST_NAME)!,
                  last_name: sharedPref.getString(LAST_NAME)!,

                  // name: sharedPref.getString(Name),
                  userName: sharedPref.getString(USER_NAME),
                  id: sharedPref.getInt(USER_ID)!,
                  email: sharedPref.getString(USER_EMAIL),
                  gender: sharedPref.getString(GENDER),
                  otherGenderText: sharedPref.getString(OTHER_GENDER_TEXT),
                ),
                screenIdentifier: ScreenIdentifier.RegistrationScreen);
          },
          title: language.handleInCompleteProfile);
    } else {
      positiveAction();
    }
  }

  static addCutomInfoWindow(
      {required CustomInfoWindowController customInfoWindowController,
      required LatLng location,
      required String title}) {
    customInfoWindowController.addInfoWindow!(
        Column(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(.6),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.account_circle,
                        color: Colors.white,
                        size: 30,
                      ),
                      SizedBox(
                        width: 8.0,
                      ),
                      Text(title)
                    ],
                  ),
                ),
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            // Triangle.isosceles(
            //   edge: Edge.BOTTOM,
            //   child: Container(
            //     color: Colors.blue,
            //     width: 20.0,
            //     height: 10.0,
            //   ),
            // ),
          ],
        ),
        location);
  }

  static Future<bool> checkNewLocationPermissionWhileInUse() async {
    // final notificationstatus = await Permission.notification.status;
    final status = await Permission.locationWhenInUse.request();

    if (status == PermissionStatus.granted) {
      GlobalState.location_permission.value = status.name;
      // Permission is already granted
      return true;
    }

    if (status == PermissionStatus.denied) {
      GlobalState.location_permission.value = status.name;
      return false;
    }

    if (status == PermissionStatus.permanentlyDenied) {
      GlobalState.location_permission.value = status.name;
      return false;
    }

    if (status == PermissionStatus.restricted) {
      GlobalState.location_permission.value = status.name;
      return false;
    }

    GlobalState.location_permission.value = status.name;
    return false;
  }

  static Future<bool> checkNewLocationPermissionAlways() async {
    final status = await Permission.locationAlways.status;

    if (status == PermissionStatus.granted) {
      GlobalState.location_permission_always.value = status.name;
      // Permission is already granted
      return true;
    }

    if (status == PermissionStatus.denied) {
      GlobalState.location_permission_always.value = status.name;

      return false;
    }

    if (status == PermissionStatus.permanentlyDenied) {
      GlobalState.location_permission_always.value = status.name;
      return false;
    }

    if (status == PermissionStatus.restricted) {
      GlobalState.location_permission_always.value = status.name;
      return false;
    }

    GlobalState.location_permission_always.value = status.name;
    return false;
  }

  static Future<bool> checkNewNotificationPermission() async {
    // final notificationstatus = await Permission.notification.status;
    final status = await Permission.notification.request();

    if (status == PermissionStatus.granted) {
      GlobalState.notification_permission.value = status.name;
      // Permission is already granted
      return true;
    }

    if (status == PermissionStatus.denied) {
      GlobalState.notification_permission.value = status.name;
      return false;
    }

    if (status == PermissionStatus.permanentlyDenied) {
      GlobalState.notification_permission.value = status.name;
      return false;
    }

    if (status == PermissionStatus.restricted) {
      GlobalState.notification_permission.value = status.name;
      return false;
    }

    GlobalState.notification_permission.value = status.name;
    return false;
  }

  static hnadleMissingPermision(
      {required BuildContext context,
      required bool notificationPermission,
      required bool locationPermssion,
      required bool microphonePermission,
      required void Function() onAllPermissionGranted}) {
    if (!notificationPermission) {
      showInfoDialog(
          context: context,
          positiveAction: () {},
          title:
              "It seems you have missing notification permissions, we need this permissions, for providing you best and proper functionality, Please click on allow for further details, Thank you");
      return;
    }
    if (!locationPermssion) {
      showInfoDialog(
          context: context,
          positiveAction: () {},
          title:
              "It seems you have missing location permissions, we need this permissions, for providing you best and proper functionality, Please click on allow for further details, Thank you");
      return;
    }
    if (!microphonePermission) {
      showInfoDialog(
          context: context,
          positiveAction: () {},
          title:
              "It seems you have missing microphone permissions, we need this permissions, for providing you best and proper functionality, Please click on allow for further details, Thank you");
      return;
    } else {
      onAllPermissionGranted();
    }
  }

  static getDarkModeSetting() {
    String darkmodeSetting = sharedPref.getString(DARKMODE_SETTING) ?? "";

    if (darkmodeSetting == DARKMODE) {
      // GlobalState.darkModeSetting.value = ThemeMode.dark;
    } else if (darkmodeSetting == LIGHTMODE) {
      // GlobalState.darkModeSetting.value = ThemeMode.light;
    } else if (darkmodeSetting == SYSTEMMODE) {
      // GlobalState.darkModeSetting.value = ThemeMode.system;
    } else {
      // GlobalState.darkModeSetting.value = ThemeMode.system;
    }
  }

  static String dateToAPIString(DateTime date) {
    String day =
        (date.day < 10) ? '0' + date.day.toString() : date.day.toString();
    String month =
        (date.month < 10) ? '0' + date.month.toString() : date.month.toString();
    // return day + '-' + month + '-' + date.year.toString();
    return date.year.toString() + '-' + month + '-' + day;
  }

  static DateTime apiStringToDate(String text) {
    List<String> data = text.split("-");
    if (data.length != 3) {
      return DateTime.now();
    }
    return DateTime.parse(data[0] + '-' + data[1] + '-' + data[2]);
  }

  static String dateToUIString(DateTime date) {
    String day =
        (date.day < 10) ? '0' + date.day.toString() : date.day.toString();
    // String month =
    //     (date.month < 10) ? '0' + date.month.toString() : date.month.toString();

    return day + '-' + getMonthName(date.month) + '-' + date.year.toString();
  }

  static DateTime uiStringToDate(String text) {
    List<String> data = text.split("-");
    if (data.length != 3) {
      return DateTime.now();
    }
    int month = getMonthNumber(data[1]);

    return DateTime.parse(data[2] +
        '-' +
        (month < 10 ? "0" + month.toString() : month.toString()) +
        '-' +
        data[0]);
  }

  static String getMonthName(int month) {
    return listOfMonths[month - 1]['abbreviation'];
  }

  static int getMonthNumber(String month) {
    for (int i = 0; i < listOfMonths.length; i++) {
      if (listOfMonths[i]['abbreviation'] == month) {
        return i + 1;
      }
    }
    return 1;
  }

  static Future<void> showWWWConnectionError({
    required BuildContext context,
  }) async {
    if (GlobalState.isInternetErrorOpen) {
      return;
    }
    GlobalState.isInternetErrorOpen = true;

    showInfoDialogNew(
        context: context,
        onClick: () {
          Navigator.pop(navigatorKey.currentContext!);
          GlobalState.isInternetErrorOpen = false;
        },
        title:
            "The internet connection is unstable. Please check your internet connection and try again.");
  }

  static Future<bool> saveStartWaitingTimeData({
    required LocalWaitingTimeModel data,
  }) async {
    List<dynamic> _savedData =
        sharedPref.getStringList("waiting_time_data") ?? [];

    List<LocalWaitingTimeModel> _existingData = [];
    _existingData = _savedData
        .map((e) => LocalWaitingTimeModel.fromMap(jsonDecode(e)))
        .toList();

    bool _isAlreadyAppended =
        _existingData.where((element) => element.type == data.type).isNotEmpty;
    if (_isAlreadyAppended) {
      return true;
    }
    _existingData.add(data);

    return await sharedPref.setStringList("waiting_time_data",
        _existingData.map((e) => jsonEncode(e.toMap())).toList());
  }

  static Future<bool> saveEndWaitingTimeData({
    required LocalWaitingTimeModel data,
  }) async {
    List<dynamic> _savedData =
        sharedPref.getStringList("waiting_time_data") ?? [];

    List<LocalWaitingTimeModel> _existingData = [];
    _existingData = _savedData
        .map((e) => LocalWaitingTimeModel.fromMap(jsonDecode(e)))
        .toList();
    if (_existingData.isEmpty) {
      return true;
    }
    _existingData.last.endTime = data.endTime;
    return await sharedPref.setStringList("waiting_time_data",
        _existingData.map((e) => jsonEncode(e.toMap())).toList());
  }

  static List<LocalWaitingTimeModel> getWaitingTimeData() {
    List<dynamic> _savedData =
        sharedPref.getStringList("waiting_time_data") ?? [];

    List<LocalWaitingTimeModel> _existingData = [];
    _existingData = _savedData
        .map((e) => LocalWaitingTimeModel.fromMap(jsonDecode(e)))
        .toList();

    _existingData.removeWhere((data) => data.endTime == null);
    return _existingData;
  }

  static Future<bool> removeWaitingTimeData() async {
    return await sharedPref.remove("waiting_time_data");
  }
}
