import 'package:flutter/material.dart';
import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/global/constants/constants.dart';

class SlidingUpHandleContainer extends StatelessWidget {
  const SlidingUpHandleContainer({super.key});


  @override
  Widget build(BuildContext context) {
    return Container(
      margin: screenPadding,
      
      height: 10,
      decoration: BoxDecoration(
          borderRadius: appRadius, color: AppColors.blackColor(context)),
      width: 100,
    );
  }
}
